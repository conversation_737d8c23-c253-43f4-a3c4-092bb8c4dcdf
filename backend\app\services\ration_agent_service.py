# Updated backend/app/services/ration_agent_service.py
from .llm_service import LLMFactory
from .formulation import formulate_ration
from .kb_service import KnowledgeBaseService
from .ration_llm_context import RationLLMContext
from ..models import Ration, Feed, AnimalGroup, RationFeed, Constraint, Nutrient, FeedNutrient # Keep model imports
from flask import current_app
import logging
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from google.genai import types # Ensure 'types' is imported
from sqlalchemy.orm import joinedload # Import joinedload

logger = logging.getLogger(__name__)

class RationAgentService:
    """
    Service for managing LLM-assisted ration formulation.
    Receives current ration state from frontend with each request.
    Provides tools for querying and modifying rations based on the provided context.
    """

    # FUNCTION_DECLARATIONS remain the same as the corrected version from the previous turn
    # (Ensuring min_value/max_value type is "number" and they are not required)
    FUNCTION_DECLARATIONS = [
       {
            "name": "get_feed_info",
            "description": "Get detailed information about multiple feeds including nutrition content.zh:获取饲料详细信息，包括营养成分",
            "parameters": {
                "type": "object",
                "properties": {
                    "feed_ids": {
                        "type": "array",
                        "description": "Array of feed IDs to get information about",
                        "items": {
                            "type": "integer",
                            "description": "ID of a feed"
                        }
                    }
                },
                "required": ["feed_ids"]
            }
        },
        {
            "name": "add_nutrients_to_feed",
            "description": "Add or update nutrients for one or multiple feeds.zh:为一个或多个饲料添加或更新营养成分",
            "parameters": {
                "type": "object",
                "properties": {
                    "feed_id": {
                        "type": "integer",
                        "description": "ID of the feed to add nutrients to (for backward compatibility)"
                    },
                    "nutrients": {
                        "type": "array",
                        "description": "Array of nutrients to add or update (for backward compatibility)",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {
                                    "type": "string",
                                    "description": "Name of the nutrient (e.g., 'Crude Protein', 'NDF')"
                                },
                                "value": {
                                    "type": "number",
                                    "description": "Value of the nutrient"
                                },
                                "unit": {
                                    "type": "string",
                                    "description": "Unit of the nutrient (e.g., '%', 'Mcal/kg')"
                                }
                            },
                            "required": ["name", "value"]
                        }
                    },
                    "feeds": {
                        "type": "array",
                        "description": "Array of feeds to update with their nutrients",
                        "items": {
                            "type": "object",
                            "properties": {
                                "feed_id": {
                                    "type": "integer",
                                    "description": "ID of the feed to add nutrients to"
                                },
                                "nutrients": {
                                    "type": "array",
                                    "description": "Array of nutrients to add or update for this feed",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "name": {
                                                "type": "string",
                                                "description": "Name of the nutrient (e.g., 'Crude Protein', 'NDF')"
                                            },
                                            "value": {
                                                "type": "number",
                                                "description": "Value of the nutrient"
                                            },
                                            "unit": {
                                                "type": "string",
                                                "description": "Unit of the nutrient (e.g., '%', 'Mcal/kg')"
                                            }
                                        },
                                        "required": ["name", "value"]
                                    }
                                }
                            },
                            "required": ["feed_id", "nutrients"]
                        }
                    }
                }
            }
        },
        {
            "name": "search_feeds",
            "description": "Search for feeds by name or description to find options that might work for your ration.zh:通过名称或描述搜索饲料，找到可能适合您配方的选项",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search terms for finding feeds (e.g., 'corn', 'protein')"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of results to return (default: 5)"
                    }
                },
                "required": ["query"]
            }
        },
        {
            "name": "get_kb_article",
            "description": "Get information from the knowledge base about animal nutrition, feed formulation, or other relevant topics.zh:从知识库获取有关动物营养、饲料配方或其他相关主题的信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "What information or topic are you looking for?"
                    }
                },
                "required": ["query"]
            }
        },
        {
            "name": "update_nutrient_constraint",
            "description": "Update or add one or more nutrient constraints for formulation.zh:更新或添加一个或多个营养约束条件",
            "parameters": {
                "type": "object",
                "properties": {
                    "constraints": {
                        "type": "array",
                        "description": "Array of nutrient constraints to update or add",
                        "items": {
                            "type": "object",
                            "properties": {
                                "nutrient_name": {
                                    "type": "string",
                                    "description": "Name of the nutrient (e.g., 'Crude Protein', 'NDF')"
                                },
                                "min_value": {
                                    "type": "number",
                                    "description": "Minimum value (omit if no minimum)"
                                },
                                "max_value": {
                                    "type": "number",
                                    "description": "Maximum value (omit if no maximum)"
                                }
                            },
                            "required": ["nutrient_name"]
                        }
                    },
                    "nutrient_name": {
                        "type": "string",
                        "description": "Name of the nutrient (for backward compatibility)"
                    },
                    "min_value": {
                        "type": "number",
                        "description": "Minimum value (for backward compatibility)"
                    },
                    "max_value": {
                        "type": "number",
                        "description": "Maximum value (for backward compatibility)"
                    }
                }
            }
        },
        {
            "name": "save_formulation",
            "description": "Save the current ration formulation to the database.zh:将当前饲料配方保存到数据库",
            "parameters": {
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Optional name override for the ration"
                    }
                }
            }
        },
        {
            "name": "finish_formulation",
            "description": "Complete the formulation process and provide a final recommendation.zh:完成配方制定过程并提供最终建议",
            "parameters": {
                "type": "object",
                "properties": {
                    "reason": {
                        "type": "string",
                        "description": "Reason for completing the formulation"
                    },
                    "success": {
                        "type": "boolean",
                        "description": "Whether formulation was successful"
                    }
                },
                "required": ["reason", "success"]
            }
        },
        {
            "name": "check_nutrition",
            "description": "Check if the current feed composition in context meets nutritional requirements.zh:检查当前上下文中的饲料组合是否满足营养需求",
        },
        {
            "name": "make_ration",
            "description": "Create a ration formulation with the specified feed inclusions in kilograms.zh:使用指定的饲料配比（公斤）创建饲料配方",
            "parameters": {
                "type": "object",
                "properties": {
                    "feed_inclusions": {
                        "type": "array",
                        "description": "Array of feed inclusions for the ration, each with feed_id and inclusion_kg (weight in kg, not percentage)",
                        "items": {
                            "type": "object",
                            "properties": {
                                "feed_id": {
                                    "type": "integer",
                                    "description": "ID of the feed to include in the ration"
                                },
                                "inclusion_kg": {
                                    "type": "number",
                                    "description": "Inclusion amount in kg (not percentage) - total should match DMI constraint"
                                }
                            },
                            "required": ["feed_id", "inclusion_kg"]
                        }
                    }
                },
                "required": ["feed_inclusions"]
            }
        }
    ]

    '''{#removed for agent
            "name": "run_formulation",
            "description": "Run ration formulation with current feeds and constraints.",
            "parameters": {
                "type": "object",
                "properties": {
                    "optimization_objective": {
                        "type": "string",
                        "enum": ["minimize_cost", "balance_nutrients"],
                        "description": "What to optimize for (default: minimize_cost)"
                    }
                }
            }
        },
        {
            "name": "add_feed_to_ration",
            "description": "Add multiple feeds to the current formulation.zh:向当前配方中添加多种饲料",
            "parameters": {
                "type": "object",
                "properties": {
                    "feeds": {
                        "type": "array",
                        "description": "Array of feeds to add",
                        "items": {
                            "type": "object",
                            "properties": {
                                "feed_id": {
                                    "type": "integer",
                                    "description": "ID of the feed to add"
                                },
                                "min_inclusion_percentage": {
                                    "type": "number",
                                    "description": "Minimum inclusion percentage (0-100)"
                                },
                                "max_inclusion_percentage": {
                                    "type": "number",
                                    "description": "Maximum inclusion percentage (0-100)"
                                }
                            }
                        }
                    }
                },
                "required": ["feeds"]
            }
        },
        {
            "name": "remove_feed_from_ration",
            "description": "Remove one or more feeds from the current formulation.zh:从当前配方中移除一种或多种饲料",
            "parameters": {
                "type": "object",
                "properties": {
                    "feed_ids": {
                        "type": "array",
                        "description": "Array of feed IDs to remove",
                        "items": {
                            "type": "integer",
                            "description": "ID of the feed to remove"
                        }
                    },
                    "feed_id": {
                        "type": "integer",
                        "description": "ID of the feed to remove (for backward compatibility)"
                    }
                }
            }
        },'''

    @staticmethod
    def process_message(user_id: int, message: str, context_data: Optional[Dict] = None, ration_id: Optional[int] = None, locale: str = 'en'):
        """Process a message using LLM with function calling."""
        logger.info(f"Processing message for user {user_id}. Ration ID: {ration_id}. Locale: {locale}")

        # Check if this is a stop command from the user
        if message.lower().strip() in ["stop", "停止"]:
            logger.info("Received stop command from user. Setting is_formulation_complete=True and completion_reason='Stopped by user'")
            if context_data:
                # Check if the context already indicates the process is stopped
                if context_data.get('is_formulation_complete') and context_data.get('completion_reason') == "Stopped by user":
                    logger.info("Process was already marked as stopped in the context. Reinforcing stop flags.")
                    # Return the context as-is with the stopped flag
                    return {
                        'response': "Formulation process already stopped.",
                        'context': context_data,
                        'stopped': True,
                        'auto_continue': False
                    }

                # Initialize a new context with the provided data
                context = RationLLMContext(user_id, ration_id, locale)
                # Populate context from data
                context.current_formulation_state = context_data.get('current_formulation_state')
                context.herd_info = context_data.get('herd_info')
                context.feed_data = context_data.get('feed_data', [])
                context.constraints = context_data.get('constraints', [])
                context.message_history = context_data.get('message_history', [])
                context.formulation_result = context_data.get('formulation_result')
                context.formulation_attempts = context_data.get('formulation_attempts', 0)
                context.iterations = context_data.get('iterations', 0)

                # Mark as complete due to user stop
                context.is_formulation_complete = True
                context.completion_reason = "Stopped by user"
                context.auto_continue = False  # Explicitly disable auto-continue

                # Add system message about stopping
                context.add_message('system', "Formulation process stopped as requested.")

                # Create context dictionary with explicit stopped flags
                context_dict = context.to_dict()

                # Ensure these flags are explicitly set in the context dictionary
                context_dict['is_formulation_complete'] = True
                context_dict['completion_reason'] = "Stopped by user"
                context_dict['auto_continue'] = False  # Explicitly disable auto-continue

                return {
                    'response': "Formulation process stopped as requested.",
                    'context': context_dict,
                    'stopped': True,
                    'auto_continue': False  # Explicitly disable auto-continue in the response
                }

        if not context_data:
            logger.error("Missing required context_data")
            return {
                'response': "Error: Missing required ration context data.",
                'context': None
            }

        context = None
        try:
            # Initialize context from provided data
            context = RationLLMContext(user_id, ration_id, locale)

            # Populate context attributes
            context.current_formulation_state = context_data.get('current_formulation_state')
            context.herd_info = context_data.get('herd_info')
            context.feed_data = context_data.get('feed_data', [])
            context.constraints = context_data.get('constraints', [])
            context.message_history = context_data.get('message_history', [])
            context.formulation_result = context_data.get('formulation_result')
            context.formulation_attempts = context_data.get('formulation_attempts', 0)
            context.iterations = context_data.get('iterations', 0)
            context.is_formulation_complete = context_data.get('is_formulation_complete', False)
            context.function_calls = context_data.get('function_calls', [])

            # Check if the process is already marked as complete due to user stop
            is_stopped = context_data.get('is_formulation_complete') and context_data.get('completion_reason') == "Stopped by user"

            # Check if this is an auto-continue message - more permissive check
            # But never auto-continue if the process is stopped
            is_auto_continue = False
            if not is_stopped:
                is_auto_continue = (
                    ("continue" in message.lower() and context_data.get('auto_continue')) or
                    context_data.get('auto_continue') == True  # Explicit check for True
                )

            logger.info(f"Auto-continue check: message='{message}', context_auto_continue={context_data.get('auto_continue')}, is_stopped={is_stopped}, is_auto_continue={is_auto_continue}")

            # Set auto_continue flag in context for future iterations
            context.auto_continue = is_auto_continue

            # Load available feeds for suggestions if needed
            if not context.available_feeds:
                context.load_available_feeds()

            # Add new user message (only if not auto-continue)
            if not is_auto_continue:
                context.add_message('user', message)

            # Increment the iteration counter to track agent progress
            context.iterations += 1

            # Get clean context for LLM
            llm_context_dict = context.get_context_for_llm()

            # Build system prompt based on locale and context
            system_prompt = RationAgentService._build_system_prompt(context)

            # Construct full prompt with context and history
            prompt = f"""
            {system_prompt}

            {RationAgentService._format_context_for_llm(llm_context_dict)}

            Conversation History:
            {RationAgentService._format_message_history(llm_context_dict)}

            {'' if is_auto_continue else f'User: {message}'}
            Assistant:
            """

            logger.info(f"prompt:{prompt}")

            # Call LLM with function declarations
            llm_provider = LLMFactory.get_current_provider()
            response = llm_provider.call_with_functions(
                prompt=prompt,
                function_declarations=RationAgentService.FUNCTION_DECLARATIONS,
                temperature=0.7,
                max_tokens=4000,
            )


            # Handle function calls or direct responses
            return RationAgentService._process_llm_response(response, context)

        except Exception as e:
            logger.exception(f"Error in process_message: {e}")
            return {
                'response': f"An internal error occurred: {str(e)}",
                'context': context.to_dict() if context else None
            }

    @staticmethod
    def _build_system_prompt(context):
        """Build a system prompt based on the current context and locale."""
        locale = context.locale or 'en'
        locale_prompt = "##除无法翻译的专有名词或缩写外，请在回答中仅使用中文。在回复文本中工具名称也需要进行翻译"
        if locale != 'zh':
            locale_prompt = f" use {locale} consistantly in response."


        prompt = """\
You are an expert agent for dairy cow ration formulation.You would use the provided tools to make a ration, you dont need user permission to use tools or make changes to the ration formulation.

**IMPORTANT:
-use get_kb_article to query the knowledge base for latest ration making knowledege befor proceed to ration making.
-DO NOT FAKE FUNCTION CALLS AND RESULTS, ALWAYS USE THE PROVIDED FUNCTION CALLS.
**

As an expert, you should proactively:
- Analyze the current formulation state to understand any issues or improvement needs
- Make appropriate recommendations and take action based on your analysis
- Add suitable feeds and nutritional constraints
- Use your expertise to directly calculate rations rather than relying on optimization algorithms
- Verify your formulations meet all nutritional requirements
- Provide expert knowledge and explanations throughout the process

ESSENTIAL REQUIREMENTS:
1. Ensure a DMI (Dry Matter Intake) constraint exists - add one if missing!
2. Formulate in kg (actual weights), not percentages
3. Match total feed inclusion kg to DMI constraint value
4. Balance nutrients according to all constraints
5. the add_nutrients_to_feed tool can only be used on feed added to the ration

Ration Calculation Guide:
1. Consider the DMI constraint
2. Use make_ration function to propose a formulation
3. You can use the check_nutrition function to verify your proposed formulation
4. Make sure you provide a balanced diet, and meet special need for user if provided
5. You are allowed to use add_nutrients_to_feed to add missing nutrients based on general knowledge without permission.

Explain your reasoning at each step. Continue until you achieve a successful formulation or determine it's not possible.

IMPORTANT: Pay close attention to the function calls and their results in the conversation history.
Use this information to guide your next actions and avoid repeating failed approaches."""
        prompt+=f"\n{locale_prompt}"

        return prompt



    @staticmethod
    def _format_context_for_llm(context_dict):
        """Format context data for the LLM prompt with detailed function call results."""
        formatted = "Current Ration Details:\n"

        # Ration basic info
        ration_name = context_dict.get('current_formulation_state', {}).get('ration_name', 'New Ration')
        formatted += f"- Name: {ration_name}\n"
        formatted += f"- Status: {'New (not yet saved)' if not context_dict.get('ration_id') else 'Existing'}\n"

        # Animal info
        if context_dict.get('herd_info'):
            herd = context_dict['herd_info']
            formatted += f"- Animal Group: {herd.get('name', 'Unknown')}\n"
            if herd.get('weight_kg'):
                formatted += f"- Weight: {herd.get('weight_kg')} kg\n"
            if herd.get('milk_production_kg'):
                formatted += f"- Milk Production: {herd.get('milk_production_kg')} kg/day\n"

        # Current selected feeds
        feeds = context_dict.get('feeds', [])
        formatted += f"\nSelected Feeds ({len(feeds)}): these are the feeds you would use for formulation.Add befor creating ration.\n"
        if feeds:
            for i, feed in enumerate(feeds, 1):
                formatted += f"{i}. {feed.get('name', 'Unknown Feed')} (min: {feed.get('min_inclusion', 0)}%, max: {feed.get('max_inclusion', 100)}%)\n"

                # Include key nutrient information for each feed
                if feed.get('nutrients'):
                    formatted += "  Recorded Nutrients:\n"
                    for nutrients in feed.get('nutrients',[]):
                        formatted += f"   - nutrient:{nutrients.get("nutrient_name")}, value:{nutrients.get("value")}\n"
        else:
            formatted += "No feeds added yet.\n"

        # Available feeds section (important for LLM to choose from)
        available_feeds = context_dict.get('available_feeds', [])
        formatted += f"\nAvailable Feeds ({len(available_feeds)}):\n"
        if available_feeds:
            for i, feed in enumerate(available_feeds[:10], 1):  # Limit to first 10 to avoid overwhelming context
                formatted += f"{i}. {feed.get('name', 'Unknown')} (ID: {feed.get('id')}, Cost: {feed.get('cost_per_kg')} per kg)\n"

                # Add a few key nutrients for each available feed
                nutrients = feed.get('nutrients', [])
                if nutrients:
                    formatted += "   Key Nutrients:\n"
                    for nutrients in feed.get('nutrients',[]):
                        formatted += f"   - nutrient:{nutrients.get("nutrient_name")}, value:{nutrients.get("value")}\n"

            if len(available_feeds) > 10:
                formatted +="\nMore available feeds:\n"
                for i, feed in enumerate(available_feeds[10:], 1):  # Limit to first 10 to avoid overwhelming context
                    formatted += f"{i}. {feed.get('name', 'Unknown')} (ID: {feed.get('id')})\n"
        else:
            formatted += "No feeds available for selection.\n"

        # Nutritional constraints
        constraints = context_dict.get('constraints', [])
        formatted += f"\nNutritional Constraints ({len(constraints)}):\n"
        if constraints:
            for constraint in constraints:
                min_val = constraint.get('min')
                max_val = constraint.get('max')
                constraint_str = f"- {constraint.get('nutrient', 'Unknown')}: "

                if min_val is not None and max_val is not None:
                    constraint_str += f"{min_val} to {max_val}"
                elif min_val is not None:
                    constraint_str += f"min {min_val}"
                elif max_val is not None:
                    constraint_str += f"max {max_val}"
                else:
                    constraint_str += "no limits set"

                formatted += constraint_str + "\n"
        else:
            formatted += "No constraints defined yet.\n"

        # DMI constraint check - highlight if missing
        has_dmi = any(c.get('nutrient') == 'DMI' for c in constraints)
        if not has_dmi:
            formatted += "\nDMI CONSTRAINT MISSING! You can have estimations from feed composition and herd stat.\n"

        return formatted

    @staticmethod
    def _format_message_history(context):
        """Format conversation history for the LLM prompt with detailed function call results."""
        message_history = context["message_history"]

        if not message_history:
            return "No previous messages."

        formatted = []
        for msg in message_history:
            role = msg.get('role', '')
            content = msg.get('content', '')

            if role == 'user':
                # Format user messages
                formatted.append(f"User: {content}")

            elif role == 'assistant':
                # Format assistant messages (with or without function calls)
                if msg.get('function_call'):
                    # Handle function call messages
                    func_call = msg.get('function_call')
                    func_name = func_call.get('name', 'unknown')
                    parames = func_call.get('arguments', '')

                    # Include any preceding text content if present
                    if content:
                        formatted.append(f"Assistant: {content}")

                    # Add the function call information
                    formatted.append(f"Assistant: [Function Call] {func_name}")
                    formatted.append(f"Assistant: [parameters:] {parames}")
                elif msg.get('is_thinking'):
                    # Handle thinking content
                    formatted.append(f"Assistant [Thinking]: {content}")
                else:
                    # Regular assistant message
                    formatted.append(f"Assistant: {content}")

            elif role == 'system' and msg.get('function_result'):
                # Format function result messages
                func_result = msg.get('function_result', {})
                func_name = func_result.get('name', 'unknown')
                result_dict = func_result.get('result', {})

                # Use the to_llm field if available (new approach)
                if result_dict.get('to_llm'):
                    formatted.append(result_dict.get('to_llm'))
                else:
                    # Fallback to basic formatting for backward compatibility
                    status = result_dict.get('status', 'unknown')
                    message = result_dict.get('message', 'No details provided')
                    formatted.append(f"System: [Function Result] {func_name} (Status: {status})\nMessage: {message}")

            elif role == 'system':
                # Format regular system messages
                formatted.append(f"System: {content}")

        # Join all formatted messages with double newlines for readability
        return "\n\n".join(formatted)

    def _process_llm_response(response, context):
        """
        Process the standardized LLM response from any provider,
        handle text, execute function calls, and prepare the response for the frontend.

        The response is expected to have the following structure:
        {
            'thinking_content': str,  # Optional reasoning/thinking content
            'content': str,           # Main response content
            'function_call': {        # Optional function call information
                'name': str,          # Function name
                'arguments': dict     # Function arguments as a dictionary
            }
        }
        """
        logger.info(f"Processing LLM response: {type(response)}")

        # Handle error cases or empty responses
        if not response:
            logger.warning("Empty LLM response received.")
            context.add_message('system', "Error: No response from model.")
            return {
                'response': "Sorry, I didn't get a valid response. Please try again.",
                'context': context.to_dict(),
                'status': 'error'
            }

        # Extract content and function call from standardized response
        content = response.get('content', '')
        thinking_content = response.get('thinking_content', '')
        function_call = response.get('function_call')

        # Store original message for reference
        original_assistant_message = content

        # Log the response components
        logger.info(f"Content: {content[:100]}...")
        if thinking_content:
            logger.info(f"Thinking content: {thinking_content[:100]}...")
        if function_call:
            logger.info(f"Function call: {function_call.get('name')} with args: {function_call.get('arguments')}")


        # --- Decide Action based on extracted parts ---

        # **Case 1: Function Call Requested (potentially with preceding text)**
        if function_call and function_call.get('name'):
            function_name = function_call.get('name')
            function_args = function_call.get('arguments', {})

            logger.info(f"Processing function call: {function_name} with args: {function_args}")

            # Add any preceding text to context first
            if content:
                context.add_message('assistant', content)

            # Add thinking content if available
            if thinking_content:
                context.add_message('assistant', f"[Thinking]: {thinking_content}", is_thinking=True)

            # Add the function call request itself to history (for context/reconstruction)
            context.add_message(
                'assistant',
                '', # No text content for this part
                function_call={'name': function_name, 'arguments': json.dumps(function_args)} # Use serializable dict
            )

            # Execute the function using internal logic
            function_result = RationAgentService._execute_function(function_name, function_args, context)

            # Add the function execution result to history
            context.add_message(
                 'system',
                 '', # No text content for this part
                 function_result={'name': function_name, 'result': function_result} # Store structured result
            )

            # Check for completion after function execution
            if function_name == 'finish_formulation':
                context.is_formulation_complete = True
                context.completion_reason = function_args.get('reason', 'No reason provided')

            if context.is_formulation_complete or context.iterations >= context.max_iterations:
                 logger.info(f"Finishing interaction after function call due to completion flag or max iterations. Reason: {context.completion_reason}")
                 final_text = function_result.get('message', f"Formulation process completed. Reason: {context.completion_reason}")
                 context.add_message('assistant', final_text)
                 return {
                    'response': final_text,
                    'function_used': function_name,
                    'function_result': function_result,
                    'context': context.to_dict(),
                    'assistant_message': original_assistant_message,
                    'thinking_content': thinking_content,
                    'auto_continue': False,
                    'status': function_name
                }

            # --- Prepare a response for the frontend ---
            logger.info(f"Function '{function_name}' executed with result: {function_result}")

            # Create a simple status message about the function execution
            if function_result.get('status') == 'success':
                function_status_text = f"Successfully executed {function_name}: {function_result.get('message', 'Operation completed')}"
            else:
                function_status_text = f"Executed {function_name} with status {function_result.get('status', 'unknown')}: {function_result.get('message', 'No details available')}"

            logger.info(f"Created status message: {function_status_text}")

            # Add the status message to context
            context.add_message('system', function_status_text)

            # Create a comprehensive response that includes original content, thinking content, and function status
            response_parts = []

            # Add original content if available
            if content:
                response_parts.append(content)

            # Add thinking content if available
            if thinking_content:
                response_parts.append(f"[Thinking]: {thinking_content}")

            # Add function status
            response_parts.append(f"[Function]: {function_status_text}")

            # Join all parts with double newlines
            comprehensive_response = "\n\n".join(response_parts)

            # Determine if we should auto-continue - always continue after a function call unless explicitly complete
            # Never auto-continue if the process is stopped by the user
            is_stopped_by_user = context.is_formulation_complete and context.completion_reason == "Stopped by user"

            should_auto_continue = (
                not context.is_formulation_complete and
                not is_stopped_by_user and
                context.iterations < context.max_iterations and
                function_result.get('status') != 'error'  # Don't auto-continue on errors by default
            )

            # Special handling for run_formulation - we want to continue even on errors
            # so the LLM can handle the error and suggest fixes
            if function_name == 'run_formulation':
                logger.info(f"Special handling for run_formulation with status: {function_result.get('status')}")

                # Check if the process is stopped by the user
                is_stopped_by_user = context.is_formulation_complete and context.completion_reason == "Stopped by user"

                if is_stopped_by_user:
                    # Never auto-continue if the process is stopped by the user
                    logger.info("Process was stopped by user. Not auto-continuing even for run_formulation.")
                    should_auto_continue = False
                    context.auto_continue = False
                else:
                    # For formulation, continue on any status (success, failed, error) to let LLM handle it
                    should_auto_continue = (
                        not context.is_formulation_complete and
                        context.iterations < context.max_iterations
                    )
                    # Always set to true for formulation to ensure the LLM can respond to the result
                    should_auto_continue = True
                    context.auto_continue = True
            # Force auto-continue to true for successful function calls in agentic mode
            elif function_result.get('status') == 'success' or (
                function_result.get('status') == 'error' and
                (function_result.get('message'))
            ):
                # Check if the process is stopped by the user
                is_stopped_by_user = context.is_formulation_complete and context.completion_reason == "Stopped by user"

                if is_stopped_by_user:
                    # Never auto-continue if the process is stopped by the user
                    logger.info("Process was stopped by user. Not auto-continuing even for successful function calls.")
                    should_auto_continue = False
                    context.auto_continue = False
                else:
                    should_auto_continue = True
                    # Set the flag in the context for future iterations
                    context.auto_continue = True

            logger.info(f"Auto-continue decision: {should_auto_continue} (is_complete={context.is_formulation_complete}, iterations={context.iterations}, max={context.max_iterations}, function_status={function_result.get('status')})")

            # Return final response and updated context
            result = {
                'response': comprehensive_response,  # Include all content in the response
                'function_used': function_name, # Indicate which function was just used
                'function_result': function_result, # Include its result
                'context': context.to_dict(),
                'assistant_message': original_assistant_message,
                'thinking_content': thinking_content,
                'auto_continue': should_auto_continue, # Signal frontend to continue automatically
                'status': function_name  # Add status for streaming updates
            }

            logger.info(f"Returning response with auto_continue={should_auto_continue}, status={function_name}")
            return result

        # **Case 2: Only Text Received**
        elif content:
            logger.info("LLM returned only text.")
            context.add_message('assistant', content)

            # Add thinking content if available
            if thinking_content:
                context.add_message('assistant', f"[Thinking]: {thinking_content}", is_thinking=True)

            # Create a comprehensive response that includes both content and thinking content
            response_parts = []

            # Add original content
            response_parts.append(content)

            # Add thinking content if available
            if thinking_content:
                response_parts.append(f"[Thinking]: {thinking_content}")

            # Join all parts with double newlines
            comprehensive_response = "\n\n".join(response_parts)

            # Determine if we should auto-continue - for text-only responses, we should continue the agentic flow
            # Check if the process is stopped by the user
            is_stopped_by_user = context.is_formulation_complete and context.completion_reason == "Stopped by user"

            should_auto_continue = (
                not context.is_formulation_complete and
                not is_stopped_by_user and
                context.iterations < context.max_iterations
            )

            # In agentic mode, always continue after text responses if not explicitly stopped
            if not context.is_formulation_complete and not is_stopped_by_user:
                logger.info("Auto-continuing after text-only response in agentic mode.")
                should_auto_continue = True
                # Set the flag in the context for future iterations
                context.auto_continue = True
            elif is_stopped_by_user:
                logger.info("Process was stopped by user. Not auto-continuing for text-only response.")
                should_auto_continue = False
                context.auto_continue = False

            logger.info(f"Text-only auto-continue decision: {should_auto_continue} (is_complete={context.is_formulation_complete}, iterations={context.iterations}, max={context.max_iterations})")

            return {
                'response': comprehensive_response,  # Include all content in the response
                'function_used': None,  # Add this field for consistency
                'function_result': None,  # Add this field for consistency
                'context': context.to_dict(),
                'assistant_message': original_assistant_message,
                'thinking_content': thinking_content,
                'auto_continue': should_auto_continue,
                'status': 'thinking'
            }

        # **Case 3: Empty Response (No Text, No Function Call)**
        else:
            logger.warning("LLM response contained no text or function calls.")
            logger.info("Adding 5-second delay before returning empty response...")
            time.sleep(5)  # Add a 5-second delay
            context.add_message('system', "Warning: Model returned empty response.")
            return {
                'response': "The agent provided an empty response.",
                'function_used': None,  # Add this field for consistency
                'function_result': None,  # Add this field for consistency
                'context': context.to_dict(),
                'auto_continue': False,  # Explicitly set to false
                'status': 'error'
            }

    @staticmethod
    def _execute_function(function_name, args, context):
        """Execute the specified function using the context."""
        logger.info(f"Executing {function_name} with context for ration {context.ration_id}")

        try:
            # Database query functions
            if function_name == 'get_feed_info':
                result = RationAgentService._get_feed_info(context, args["feed_ids"])
            elif function_name == 'search_feeds':
                limit = args.get('limit', 5)
                result = RationAgentService._search_feeds(args['query'], limit)
            elif function_name == 'get_kb_article':
                result = RationAgentService._get_kb_article(args['query'])
            # Context modification functions
            elif function_name == 'add_feed_to_ration':
                result = RationAgentService._add_feed_to_ration(
                    context,
                    feeds=args.get('feeds')
                )
            elif function_name == 'remove_feed_from_ration':
                result = RationAgentService._remove_feed_from_ration(
                    context,
                    feed_id=args.get('feed_id'),
                    feed_ids=args.get('feed_ids')
                )
            elif function_name == 'update_nutrient_constraint':
                result = RationAgentService._update_nutrient_constraint(context, args)
            # New LLM-based formulation functions
            elif function_name == 'check_nutrition':
                result = RationAgentService._check_nutrition(context)
            elif function_name == 'make_ration':
                result = RationAgentService._make_ration(context, args)
            elif function_name == 'add_nutrients_to_feed':
                result = RationAgentService._add_nutrients_to_feed(
                    context,
                    feed_id=args.get('feed_id'),
                    nutrients=args.get('nutrients'),
                    feeds=args.get('feeds')
                )
            # Legacy and utility functions
            elif function_name == 'run_formulation':
                objective = args.get('optimization_objective', 'minimize_cost')
                result = RationAgentService._run_formulation(context, objective)
            elif function_name == 'save_formulation':
                name_override = args.get('name')
                result = RationAgentService._save_formulation(context, name_override)
            elif function_name == 'finish_formulation':
                result = RationAgentService._finish_formulation(
                    context,
                    args.get('reason', 'No reason provided'),
                    args.get('success', False)
                )
            else:
                logger.error(f"Unknown function: {function_name}")
                result = {'status': 'error', 'message': f"Unknown function: {function_name}"}

            # Log the function call with detailed result data
            if not hasattr(context, 'function_calls'):
                context.function_calls = []

            # Ensure we're adding to the list, not replacing it
            context.function_calls.append({
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'function': function_name,
                'args': args,
                'status': result.get('status'),
                'message': result.get('message'),
                'result_data': result  # Store full result for better context
            })

            return result

        except Exception as e:
            logger.error(f"Error executing {function_name}: {str(e)}")
            error_result = {'status': 'error', 'message': f"Error: {str(e)}"}

            # Log the error
            if hasattr(context, 'function_calls'):
                context.function_calls.append({
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'function': function_name,
                    'args': args,
                    'status': 'error',
                    'message': str(e)
                })

            return error_result

    @staticmethod
    def _get_feed_info(context, feed_ids):
        """
        Get detailed information about multiple feeds by their IDs,
        prioritizing data already loaded in the context (feed_data, available_feeds).
        Does NOT query the database if feeds are not found in the context.
        """
        if not context:
            logger.error("_get_feed_info: Context is missing")
            return {'status': 'error', 'message': "Context is missing"}

        try:
            # --- Validate and Sanitize Input ---
            if not feed_ids or not isinstance(feed_ids, list):
                return {'status': 'error', 'message': "Input 'feed_ids' must be a non-empty list."}

            valid_requested_ids = set()
            invalid_formats = []
            for feed_id in feed_ids:
                try:
                    valid_requested_ids.add(int(feed_id))
                except (ValueError, TypeError):
                    invalid_formats.append(str(feed_id))

            if not valid_requested_ids:
                return {'status': 'error', 'message': f"No valid integer feed IDs provided. Invalid formats received: {invalid_formats or 'None'}"}

            if invalid_formats:
                logger.warning(f"_get_feed_info: Invalid feed ID formats skipped: {invalid_formats}")

            # --- Build Feed Map from Context ---
            feed_map = {}
            # 1. Check currently selected feeds (context.feed_data)
            if hasattr(context, 'feed_data') and isinstance(context.feed_data, list):
                for feed_dict in context.feed_data:
                    if not isinstance(feed_dict, dict): continue
                    feed_id_key = feed_dict.get('id') or feed_dict.get('feed_id')
                    try:
                        int_feed_id = int(feed_id_key)
                        if int_feed_id in valid_requested_ids:
                            feed_map[int_feed_id] = feed_dict
                    except (ValueError, TypeError, AttributeError): pass

            # 2. Check available feeds (context.available_feeds), load if necessary
            # Ensure available_feeds is loaded if we haven't found all requested IDs yet
            ids_still_needed = valid_requested_ids - set(feed_map.keys())
            if ids_still_needed:
                if not hasattr(context, 'available_feeds') or not context.available_feeds:
                    if hasattr(context, 'load_available_feeds') and callable(context.load_available_feeds):
                        logger.info("Loading available_feeds into context for _get_feed_info.")
                        context.load_available_feeds()
                    else:
                        logger.warning("context.available_feeds is empty and load method doesn't exist.")

                if hasattr(context, 'available_feeds') and isinstance(context.available_feeds, list):
                    logger.info(f"Checking context.available_feeds for missing IDs: {list(ids_still_needed)}")
                    for feed_dict in context.available_feeds:
                        if not isinstance(feed_dict, dict): continue
                        feed_id_key = feed_dict.get('id') or feed_dict.get('feed_id')
                        try:
                            int_feed_id = int(feed_id_key)
                            # Add only if it's one we need and not already found in feed_data
                            if int_feed_id in ids_still_needed and int_feed_id not in feed_map:
                                feed_map[int_feed_id] = feed_dict
                        except (ValueError, TypeError, AttributeError): pass

            # --- Collect Results and Identify Missing Feeds ---
            results_data = []
            found_ids = set()
            not_found_in_context_ids = set()

            for requested_id in valid_requested_ids:
                if requested_id in feed_map:
                    # Assuming feed_map contains dicts potentially created by Feed.to_dict()
                    feed_info_dict = feed_map[requested_id]
                    # Basic check if nutrients seem present; add_feed should ensure they are loaded correctly usually.
                    if 'nutrients' not in feed_info_dict:
                         logger.warning(f"Feed {requested_id} found in context map, but 'nutrients' key missing. Returning potentially incomplete data.")
                         # Optionally, you could try a targeted DB fetch here ONLY for nutrients if needed,

                    results_data.append(feed_info_dict)
                    found_ids.add(requested_id)
                else:
                    not_found_in_context_ids.add(requested_id)

            # --- Determine Status and Message ---
            message_parts = []
            if found_ids:
                final_status = 'success'
                message_parts.append(f"Successfully retrieved data for {len(found_ids)} feed(s) from context.")
                if not_found_in_context_ids:
                    final_status = 'partial' # Success for some, but not all requested found in context
                    message_parts.append(f"Could not find data in context for IDs: {list(not_found_in_context_ids)}.")
            elif not_found_in_context_ids: # None found
                 final_status = 'warning'
                 message_parts.append(f"No feeds found in context for the valid requested IDs: {list(valid_requested_ids)}.")
            else: # Should not happen if valid_requested_ids is not empty, but handle defensively
                final_status = 'error'
                message_parts.append("Unexpected state: No feeds found or not found.")


            if invalid_formats:
                message_parts.append(f"Skipped invalid ID formats: {invalid_formats}.")
                if final_status == 'success' and not not_found_in_context_ids: # Only invalid formats were the issue
                     final_status = 'partial'


            # Create to_llm formatted message for LLM consumption
            to_llm_message = [f"System: [Function Result] get_feed_info (Status: {final_status})", f"Message: {' '.join(message_parts)}"]

            # Add feed details to the formatted message
            if results_data:
                to_llm_message.append("Feed Details:")
                for feed in results_data:
                    name = feed.get('name', f"Feed {feed.get('id')}")
                    nutrients = feed.get('nutrients', [])
                    to_llm_message.append(f"  - {name} (ID: {feed.get('id')})")
                    to_llm_message.append(f"    Nutrients:")

                    if nutrients and isinstance(nutrients, list):
                        # Iterate through the list of nutrient dictionaries
                        nutrient_strs = []
                        for nutrient_item in nutrients:
                            # Check for both possible key names for nutrient name
                            n_name = nutrient_item.get('nutrient_name') or nutrient_item.get('name', 'Unknown Nutrient')
                            n_value = nutrient_item.get('value', 'N/A')
                            n_unit = nutrient_item.get('unit', '')
                            nutrient_strs.append(f"      - {n_name}: {n_value} {n_unit}".strip())
                        nutrient_str = "\n".join(nutrient_strs)
                    elif isinstance(nutrients, dict) and nutrients:
                        nutrient_str = "\n".join([f"      - {n}: {v}" for n, v in nutrients.items()])
                    else:
                        nutrient_str = "      No nutrients data"

                    to_llm_message.append(nutrient_str)
                    to_llm_message.append(f"    Cost: {feed.get('cost_per_kg', 0)} per kg")
            else:
                to_llm_message.append("  No feeds found")

            return {
                'status': final_status,
                'message': " ".join(message_parts),
                'data': results_data, # Contains only data found in context
                'to_llm': "\n".join(to_llm_message)
            }

        except Exception as e:
            logger.exception(f"Critical error in _get_feed_info (context-only): {e}") # Log the full traceback
            # Return a generic error to the caller (LLM/frontend)
            return {'status': 'error', 'message': "An internal error occurred while retrieving feed information from context."}

    @staticmethod
    def _search_feeds(query, limit=5):
        """Search for feeds by name or description."""
        try:
            from ..models import Feed
            from sqlalchemy import or_

            # Search for feeds containing the query in name or description
            feeds = Feed.query.filter(
                or_(
                    Feed.name.ilike(f"%{query}%"),
                    Feed.description.ilike(f"%{query}%")
                )
            ).limit(limit).all()

            if not feeds:
                return {'status': 'warning', 'message': f"No feeds found matching '{query}'", 'data': []}

            # Return simplified feed data
            feed_data = [
                {
                    'id': feed.id,
                    'name': feed.name,
                    'description': feed.description,
                    'dry_matter_percentage': feed.dry_matter_percentage,
                    'cost_per_kg': feed.cost_per_kg
                }
                for feed in feeds
            ]

            # Create to_llm formatted message for LLM consumption
            to_llm_message = [f"System: [Function Result] search_feeds (Status: success)", f"Message: Found {len(feed_data)} feeds matching '{query}'"]

            # Add feed details to the formatted message
            if feed_data:
                to_llm_message.append("Found Feeds:")
                for feed in feed_data:
                    to_llm_message.append(f"  - {feed.get('name')} (ID: {feed.get('id')}, Cost: {feed.get('cost_per_kg', 0)} per kg)")
            else:
                to_llm_message.append("  No feeds found")

            return {
                'status': 'success',
                'message': f"Found {len(feed_data)} feeds matching '{query}'",
                'data': feed_data,
                'to_llm': "\n".join(to_llm_message)
            }

        except Exception as e:
            logger.error(f"Error in _search_feeds: {e}")
            return {'status': 'error', 'message': f"Database error: {str(e)}"}

    @staticmethod
    def _get_kb_article(query):
        """Get information from the knowledge base."""
        try:
            # Use semantic search from KnowledgeBaseService
            results = KnowledgeBaseService.semantic_search(query, limit=5)

            if not results:
                # Fall back to keyword search
                results = KnowledgeBaseService.keyword_search(query, limit=5)

            if not results:
                # Create to_llm formatted message for empty results
                to_llm_message = [
                    f"System: [Function Result] get_kb_article (Status: warning)",
                    f"Message: No knowledge base articles found for query: '{query}'"
                ]

                return {
                    'status': 'warning',
                    'message': f"No knowledge base articles found for query: '{query}'",
                    'data': [],
                    'to_llm': "\n".join(to_llm_message)  # Ensure to_llm is always present
                }

            # Log the structure of the results for debugging
            logger.debug(f"KB search results structure: {[list(article.keys()) for article in results]}")
            for i, article in enumerate(results):
                logger.debug(f"Article {i+1} summary: {article.get('summary', 'None')[:100]}...")

            # Create to_llm formatted message for LLM consumption
            to_llm_message = [f"System: [Function Result] get_kb_article (Status: success)", f"Message: Found {len(results)} relevant articles"]

            # Add article details to the formatted message
            if results:
                to_llm_message.append("Relevant Articles:")
                for article in results:
                    to_llm_message.append(f"  - {article.get('title')}")
                    # For proper RAG, we need to include the chunk content
                    # In both semantic and keyword search, the relevant chunk is in the 'summary' field
                    content_to_show = article.get('summary')
                    to_llm_message.append(f"    Content: {content_to_show}")
            else:
                to_llm_message.append("  No articles found")

            return {
                'status': 'success',
                'message': f"Found {len(results)} relevant articles",
                'data': results,
                'to_llm': "\n".join(to_llm_message)
            }

        except Exception as e:
            logger.error(f"Error in _get_kb_article: {e}")
            # Ensure to_llm is present even in error cases
            to_llm_message = [
                f"System: [Function Result] get_kb_article (Status: error)",
                f"Message: Knowledge base error: {str(e)}"
            ]
            return {
                'status': 'error',
                'message': f"Knowledge base error: {str(e)}",
                'to_llm': "\n".join(to_llm_message)
            }

    @staticmethod
    def _add_feed_to_ration(context, feeds):
        """Add multiple feeds to the ration context."""
        if not context:
            return {'status': 'error', 'message': "Context is missing"}

        if not feeds or not isinstance(feeds, list):
            return {'status': 'error', 'message': "Feeds must be a non-empty list of feed objects"}

        added_feed_names = []
        error_messages = []

        for feed_item in feeds:
            feed_id = feed_item.get('feed_id')
            min_inclusion = feed_item.get('min_inclusion_percentage', 0)
            max_inclusion = feed_item.get('max_inclusion_percentage', 100)

            # Validate parameters
            try:
                feed_id = int(feed_id)
                min_inclusion = float(min_inclusion) if min_inclusion is not None else 0
                max_inclusion = float(max_inclusion) if max_inclusion is not None else 100
            except (ValueError, TypeError):
                error_messages.append(f"Invalid parameters for feed ID {feed_id}")
                continue

            # Validate inclusion range
            if min_inclusion < 0 or min_inclusion > 100 or max_inclusion < 0 or max_inclusion > 100 or min_inclusion > max_inclusion:
                error_messages.append(f"Invalid inclusion range for feed ID {feed_id}: min={min_inclusion}, max={max_inclusion}")
                continue

            # Get feed details
            feed_info = RationAgentService._get_feed_info(context,[feed_id])
            if feed_info['status'] != 'success' or not feed_info['data']:
                error_messages.append(f"Feed ID {feed_id} not found")
                continue

            # Add to context
            result = context.add_feed(
                feed_id=feed_id,
                feed_detail=feed_info['data'][0],  # _get_feed_info returns a list, take first item
                min_inclusion=min_inclusion,
                max_inclusion=max_inclusion
            )

            if result:
                added_feed_names.append(feed_info['data'][0].get('name', f"Feed {feed_id}"))
            else:
                error_messages.append(f"Feed ID {feed_id} already exists in the ration")

        # Reset formulation state when feeds change
        if added_feed_names:
            context.formulation_result = None

        # Prepare response message
        if added_feed_names:
            feed_names = ", ".join(added_feed_names)
            message = f"Added feeds to the ration: {feed_names}"
            status = 'success' if not error_messages else 'partial'
        elif error_messages:
            message = f"Failed to add feeds: {'; '.join(error_messages)}"
            status = 'error'
        else:
            message = "No feeds were added (all already exist)"
            status = 'warning'

        # Create to_llm formatted message for LLM consumption
        to_llm_message = [f"System: [Function Result] add_feed_to_ration (Status: {status})", f"Message: {message}"]

        # Add details to the formatted message
        success_count = len(added_feed_names)
        already_exists_count = len(feeds) - success_count - len(error_messages)
        error_count = len(error_messages)

        to_llm_message.append(f"Added Feeds: {success_count}")
        to_llm_message.append(f"Already Existed: {already_exists_count}")
        to_llm_message.append(f"Errors: {error_count}")

        if added_feed_names:
            to_llm_message.append("Added Feed Names:")
            for name in added_feed_names:
                to_llm_message.append(f"  - {name}")

        return {
            'status': status,
            'message': message,
            'added_feed_names': added_feed_names,
            'success_count': success_count,
            'already_exists_count': already_exists_count,
            'error_count': error_count,
            'errors': error_messages,
            'to_llm': "\n".join(to_llm_message)
        }

    @staticmethod
    def _remove_feed_from_ration(context, feed_id=None, feed_ids=None):
        """Remove one or more feeds from the ration context."""
        if not context:
            return {'status': 'error', 'message': "Context is missing"}

        # Handle both single feed and multiple feeds
        if feed_ids is None:
            feed_ids = []

        # For backward compatibility, if feed_id is provided, add it to feed_ids
        if feed_id is not None:
            feed_ids.append(feed_id)

        if not feed_ids:
            return {'status': 'error', 'message': "No feed IDs provided to remove"}

        success_count = 0
        not_found_count = 0
        error_count = 0
        removed_feed_names = []

        for feed_id in feed_ids:
            try:
                feed_id = int(feed_id)
            except (ValueError, TypeError):
                error_count += 1
                continue

            # Find feed name before removal
            feed_name = None
            for feed in context.feed_data:
                if feed.get('id') == feed_id or feed.get('feed_id') == feed_id:
                    feed_name = feed.get('name', f"Feed {feed_id}")
                    break

            # Remove from context
            result = context.remove_feed(feed_id)

            if result:
                success_count += 1
                removed_feed_names.append(feed_name or f"Feed {feed_id}")
            else:
                not_found_count += 1

        # Reset formulation state when feeds change
        if success_count > 0:
            context.formulation_result = None

        # Prepare response message
        if success_count > 0:
            feed_names = ", ".join(removed_feed_names)
            message = f"Removed {success_count} feed(s) from the ration: {feed_names}"
            status = 'success'
        elif not_found_count > 0 and error_count == 0:
            message = f"None of the {not_found_count} feed(s) were found in the ration"
            status = 'warning'
        elif error_count > 0 and success_count == 0 and not_found_count == 0:
            message = f"Failed to remove any feeds due to errors"
            status = 'error'
        else:
            message = f"Removed {success_count} feed(s), {not_found_count} not found, {error_count} failed"
            status = 'partial'

        # Create to_llm formatted message for LLM consumption
        to_llm_message = [f"System: [Function Result] remove_feed_from_ration (Status: {status})", f"Message: {message}"]

        # Add details to the formatted message
        to_llm_message.append(f"Removed Feeds: {success_count}")
        to_llm_message.append(f"Not Found: {not_found_count}")
        to_llm_message.append(f"Errors: {error_count}")

        if removed_feed_names:
            to_llm_message.append("Removed Feed Names:")
            for name in removed_feed_names:
                to_llm_message.append(f"  - {name}")

        return {
            'status': status,
            'message': message,
            'success_count': success_count,
            'not_found_count': not_found_count,
            'error_count': error_count,
            'removed_feed_names': removed_feed_names,
            'to_llm': "\n".join(to_llm_message)
        }

    @staticmethod
    def _update_nutrient_constraint(context, constraint_data):
        """Update or add one or more nutrient constraints."""
        logger.info(f"Executing _update_nutrient_constraint with data: {constraint_data}")

        if not context:
            logger.error("Context is missing in _update_nutrient_constraint")
            return {'status': 'error', 'message': "Context is missing"}

        # Handle both single constraint and multiple constraints
        constraints = constraint_data.get('constraints', [])
        logger.info(f"Found {len(constraints)} constraints in the data")

        # For backward compatibility, if nutrient_name is provided, add it to constraints
        if constraint_data.get('nutrient_name'):
            logger.info(f"Adding single constraint for {constraint_data.get('nutrient_name')}")
            constraints.append({
                'nutrient_name': constraint_data.get('nutrient_name'),
                'min_value': constraint_data.get('min_value'),
                'max_value': constraint_data.get('max_value')
            })

        if not constraints:
            return {'status': 'error', 'message': "No constraints provided to update"}

        success_count = 0
        update_count = 0
        add_count = 0
        error_count = 0
        updated_constraints = []

        for i, constraint_item in enumerate(constraints):
            logger.info(f"Processing constraint {i+1}/{len(constraints)}")
            nutrient_name = constraint_item.get('nutrient_name')
            if not nutrient_name:
                logger.warning(f"Constraint missing nutrient_name: {constraint_item}")
                error_count += 1
                continue

            min_value = constraint_item.get('min_value')
            max_value = constraint_item.get('max_value')
            logger.info(f"Constraint for {nutrient_name}: min={min_value}, max={max_value}")

            # Validate values
            try:
                if min_value is not None:
                    min_value = float(min_value)
                    logger.info(f"Converted min_value to float: {min_value}")
                if max_value is not None:
                    max_value = float(max_value)
                    logger.info(f"Converted max_value to float: {max_value}")
            except (ValueError, TypeError) as e:
                logger.error(f"Error converting constraint values to float: {e}")
                error_count += 1
                continue

            # Check for inconsistent min/max
            if min_value is not None and max_value is not None and min_value > max_value:
                logger.error(f"Inconsistent min/max values: min={min_value}, max={max_value}")
                error_count += 1
                continue

            # Update context
            updated_constraint = {
                'nutrient_name': nutrient_name,
                'min_value': min_value,
                'max_value': max_value
            }

            # Find existing constraint
            existing = next((c for c in context.constraints if c.get('nutrient_name') == nutrient_name), None)
            logger.info(f"Existing constraint for {nutrient_name}: {existing}")

            if existing:
                # Update existing constraint
                logger.info(f"Updating existing constraint: {existing} with {updated_constraint}")
                existing.update(updated_constraint)
                update_count += 1
            else:
                # Add new constraint
                logger.info(f"Adding new constraint: {updated_constraint}")
                context.constraints.append(updated_constraint)
                add_count += 1

            success_count += 1

            # Add constraint details for message
            constraint_desc = []
            if min_value is not None:
                constraint_desc.append(f"min: {min_value}")
            if max_value is not None:
                constraint_desc.append(f"max: {max_value}")

            constraint_info = f"{nutrient_name}"
            if constraint_desc:
                constraint_info += f" ({', '.join(constraint_desc)})"
            else:
                constraint_info += " (no limits)"

            updated_constraints.append(constraint_info)

        # Reset formulation state when constraints change
        if success_count > 0:
            logger.info("Resetting formulation result due to constraint changes")
            context.formulation_result = None

        # Prepare response message
        if success_count > 0:
            constraints_text = ", ".join(updated_constraints)
            if len(updated_constraints) > 3:
                constraints_text = ", ".join(updated_constraints[:3]) + f", and {len(updated_constraints) - 3} more"

            if add_count > 0 and update_count > 0:
                message = f"Updated {update_count} and added {add_count} nutrient constraints: {constraints_text}"
            elif add_count > 0:
                message = f"Added {add_count} nutrient constraints: {constraints_text}"
            else:
                message = f"Updated {update_count} nutrient constraints: {constraints_text}"

            status = 'success'
            logger.info(f"Constraint update successful: {message}")
        elif error_count > 0:
            message = f"Failed to update any constraints due to errors"
            status = 'error'
            logger.error(f"Constraint update failed: {message}")
        else:
            message = f"No constraints were updated"
            status = 'warning'
            logger.warning(f"Constraint update warning: {message}")

        # Create to_llm formatted message for LLM consumption
        to_llm_message = [f"System: [Function Result] update_nutrient_constraint (Status: {status})", f"Message: {message}"]

        # Add details to the formatted message
        to_llm_message.append(f"Total Constraints Updated/Added: {success_count}")
        to_llm_message.append(f"Updated Existing: {update_count}")
        to_llm_message.append(f"Added New: {add_count}")
        to_llm_message.append(f"Errors: {error_count}")

        if updated_constraints:
            to_llm_message.append("Constraints:")
            for constraint_str in updated_constraints:
                to_llm_message.append(f"  - {constraint_str}")

        result = {
            'status': status,
            'message': message,
            'success_count': success_count,
            'update_count': update_count,
            'add_count': add_count,
            'error_count': error_count,
            'updated_constraints': updated_constraints,
            'to_llm': "\n".join(to_llm_message)
        }

        logger.info(f"Returning from _update_nutrient_constraint: {result}")
        return result

    @DeprecationWarning
    @staticmethod
    def _run_formulation(context, optimization_objective='minimize_cost'):
        """Run ration formulation with current context state."""
        logger.info(f"Running formulation with objective: {optimization_objective}")
        if not context:
            return {'status': 'error', 'message': "Context is missing"}

        # Check for basic requirements
        if len(context.feed_data) < 2:
            return {
                'status': 'error',
                'message': f"Need at least 2 feeds for formulation. Currently have {len(context.feed_data)}.",
                'suggestions': ["Add more feeds before attempting formulation"]
            }

        # Check if all feeds have nutrition data
        feeds_missing_data = []
        for feed in context.feed_data:
            nutrients = feed.get('nutrients', {})
            if not nutrients or (isinstance(nutrients, dict) and len(nutrients) == 0):
                feeds_missing_data.append(feed.get('name', f"Feed {feed.get('id')}"))

        if feeds_missing_data:
            feed_names = ", ".join(feeds_missing_data)
            return {
                'status': 'error',
                'message': f"Some feeds are missing nutrition data: {feed_names}",
                'suggestions': ["Replace these feeds with alternatives that have complete nutrition data"]
            }

        # Make sure we have constraints
        if not context.constraints:
            return {
                'status': 'warning',
                'message': "No nutritional constraints defined. Formulation will only optimize for cost.",
                'suggestions': ["Consider adding basic nutritional constraints for better results"]
            }

        # Get feed data in the correct format
        feeds_for_formulation = context.get_feeds_for_formulation()

        # Increment attempt counter
        context.formulation_attempts += 1

        # Get nutrient mappings for better messages
        try:
            from ..services.db_schema_service import get_nutrients_data
            all_nutrients_db = get_nutrients_data()
            nutrient_mappings = {
                data['name_en']: {'name_zh': data['name_zh'], 'unit': data['unit']}
                for _, data in all_nutrients_db.items()
                if data.get('name_en')
            }
        except Exception as e:
            logger.warning(f"Could not get nutrient mappings: {e}")
            nutrient_mappings = {}

        try:
            # Call formulation service
            result = formulate_ration(
                feeds_data=feeds_for_formulation,
                constraints_data=context.constraints,
                time_limit_seconds=10.0,
                optimization_objective=optimization_objective,
                nutrient_name_mappings=nutrient_mappings
            )

            # Add optimization objective to result for tracking
            result['optimization_objective'] = optimization_objective

            # Update context with result
            context.update_formulation_result(result)

            # Log function call count after update
            logger.info(f"Function calls after formulation: {len(getattr(context, 'function_calls', []))}")

            # Return result with appropriate message
            if result['status'] == 'success':
                total_cost = result.get('actual_total_cost', 0)
                # Create to_llm formatted message for LLM consumption
                to_llm_message = [f"System: [Function Result] run_formulation (Status: success)", f"Message: Formulation successful. Total cost: {total_cost:.2f} per day."]

                # Add details to the formatted message
                to_llm_message.append(f"Optimization Objective: {optimization_objective}")
                to_llm_message.append(f"Total Cost: {total_cost:.2f}")

                # Add feed inclusions if available
                feeds_data = result.get('feeds', {})
                if feeds_data:
                    to_llm_message.append("Feed Inclusions:")
                    for feed_id, feed_data in feeds_data.items():
                        feed_name = feed_data.get('name', f"Feed {feed_id}")
                        inclusion_pct = feed_data.get('inclusion_percentage', 0)
                        cost_contrib = feed_data.get('cost_contribution', 0)
                        to_llm_message.append(f"  - {feed_name} (ID: {feed_id}): {inclusion_pct:.2f}%, Cost: {cost_contrib:.2f}")

                # Add nutrient profile if available
                nutrients_data = result.get('nutrients', {})
                if nutrients_data:
                    to_llm_message.append("Nutrient Profile:")
                    for nutrient, data in nutrients_data.items():
                        actual = data.get('actual_value')
                        min_val = data.get('min_value')
                        max_val = data.get('max_value')

                        constraint_range = []
                        if min_val is not None: constraint_range.append(f"Min: {min_val}")
                        if max_val is not None: constraint_range.append(f"Max: {max_val}")
                        range_str = f" ({', '.join(constraint_range)})" if constraint_range else ""

                        actual_str = f"{actual:.2f}" if isinstance(actual, (int, float)) else str(actual)
                        to_llm_message.append(f"  - {nutrient}: {actual_str}{range_str}")

                response = {
                    'status': 'success',
                    'message': f"Formulation successful. Total cost: {total_cost:.2f} per day.",
                    'result': result,
                    'to_llm': "\n".join(to_llm_message)
                }
                logger.info("Formulation successful, returning response")
                return response
            else:
                # Structured response for failure
                message = result.get('message', 'Formulation failed')

                # Create to_llm formatted message for LLM consumption
                to_llm_message = [f"System: [Function Result] run_formulation (Status: failed)", f"Message: {message}"]

                # Add details about conflicts if available
                conflicts = result.get('conflicting_constraints', [])
                if conflicts:
                    to_llm_message.append("Conflicting Constraints:")
                    for conflict in conflicts:
                        to_llm_message.append(f"  - {conflict}")

                # Add any suggestions if available
                suggestions = result.get('suggestions', [])
                if suggestions:
                    to_llm_message.append("Suggestions:")
                    for suggestion in suggestions:
                        to_llm_message.append(f"  - {suggestion}")

                response = {
                    'status': 'failed',
                    'message': message,
                    'conflicts': conflicts,
                    'result': result,
                    'to_llm': "\n".join(to_llm_message)
                }
                logger.info(f"Formulation failed: {message}")
                return response

        except Exception as e:
            logger.error(f"Error in formulation service: {e}")
            error_message = f"Formulation service error: {str(e)}"
            to_llm_message = [
                f"System: [Function Result] run_formulation (Status: error)",
                f"Message: {error_message}",
                "Error Details:",
                f"  - {str(e)}"
            ]

            return {
                'status': 'error',
                'message': error_message,
                'to_llm': "\n".join(to_llm_message)
            }

    @staticmethod
    def _save_formulation(context, name_override=None):
        """Save the current formulation to the database."""
        if not context:
            return {'status': 'error', 'message': "Context is missing"}

        logger.info("save formulation with context{context}")

        from ..models import Ration, RationFeed, Constraint
        from ..extensions import db

        try:
            if context.ration_id:
                # Update existing ration
                ration = Ration.query.get(context.ration_id)
                if not ration:
                    return {'status': 'error', 'message': f"Ration with ID {context.ration_id} not found"}

                # Update basic info
                if name_override:
                    ration.name = name_override
                elif context.current_formulation_state and context.current_formulation_state.get('ration_name'):
                    ration.name = context.current_formulation_state['ration_name']

                if context.current_formulation_state and context.current_formulation_state.get('description'):
                    ration.description = context.current_formulation_state['description']

                # If formulation was run successfully, update timestamp
                if context.formulation_result and context.formulation_result.get('status') == 'success':
                    ration.last_formulated_at = datetime.now(timezone.utc)

                # Delete existing feeds and constraints
                db.session.query(RationFeed).filter(RationFeed.ration_id == context.ration_id).delete()
                db.session.query(Constraint).filter(Constraint.ration_id == context.ration_id).delete()

                # Add current feeds and constraints
                for feed in context.feed_data:
                    feed_id = feed.get('id') or feed.get('feed_id')
                    if not feed_id:
                        continue

                    db.session.add(RationFeed(
                        ration_id=context.ration_id,
                        feed_id=feed_id,
                        min_inclusion_percentage=feed.get('min_inclusion_percentage', 0),
                        max_inclusion_percentage=feed.get('max_inclusion_percentage', 100),
                        actual_inclusion_percentage=feed.get('actual_inclusion_percentage'),
                        cost_contribution=feed.get('cost_contribution')
                    ))

                for constraint in context.constraints:
                    if not constraint.get('nutrient_name'):
                        continue

                    db.session.add(Constraint(
                        ration_id=context.ration_id,
                        nutrient_name=constraint['nutrient_name'],
                        min_value=constraint.get('min_value'),
                        max_value=constraint.get('max_value'),
                        actual_value=constraint.get('actual_value')
                    ))

                db.session.commit()

                to_llm_message = [
                    f"System: [Function Result] save_formulation (Status: success)",
                    f"Message: Updated ration '{ration.name}' successfully",
                    f"Saved Ration ID: {context.ration_id}",
                    "Note: Ration state saved (feeds, constraints)."
                ]

                return {
                    'status': 'success',
                    'message': f"Updated ration '{ration.name}' successfully",
                    'ration_id': context.ration_id,
                    'to_llm': "\n".join(to_llm_message)
                }

            else:
                # Create new ration
                if not context.current_formulation_state:
                    return {'status': 'error', 'message': "Missing ration information"}

                # Get name and description
                ration_name = name_override
                if not ration_name and context.current_formulation_state:
                    ration_name = context.current_formulation_state.get('ration_name')

                if not ration_name:
                    return {'status': 'error', 'message': "Ration name is required"}

                ration_description = ''
                if context.current_formulation_state:
                    ration_description = context.current_formulation_state.get('description', '')

                # Get animal group
                animal_group_id = None
                if context.current_formulation_state:
                    animal_group_id = context.current_formulation_state.get('animal_group_id')

                if not animal_group_id and context.herd_info:
                    animal_group_id = context.herd_info.get('id')

                if not animal_group_id:
                    return {'status': 'error', 'message': "Animal group is required"}

                # Create the ration
                new_ration = Ration(
                    user_id=context.user_id,
                    name=ration_name,
                    description=ration_description,
                    animal_group_id=animal_group_id,
                    import_status='confirmed'
                )

                # Set formulation timestamp if available
                if context.formulation_result and context.formulation_result.get('status') == 'success':
                    new_ration.last_formulated_at = datetime.now(timezone.utc)

                db.session.add(new_ration)
                db.session.flush()

                # Get the new ration ID
                new_ration_id = new_ration.id
                context.ration_id = new_ration_id

                # Add feeds
                for feed in context.feed_data:
                    feed_id = feed.get('id') or feed.get('feed_id')
                    if not feed_id:
                        continue

                    db.session.add(RationFeed(
                        ration_id=new_ration_id,
                        feed_id=feed_id,
                        min_inclusion_percentage=feed.get('min_inclusion_percentage', 0),
                        max_inclusion_percentage=feed.get('max_inclusion_percentage', 100),
                        actual_inclusion_percentage=feed.get('actual_inclusion_percentage'),
                        cost_contribution=feed.get('cost_contribution')
                    ))

                # Add constraints
                for constraint in context.constraints:
                    if not constraint.get('nutrient_name'):
                        continue

                    db.session.add(Constraint(
                        ration_id=new_ration_id,
                        nutrient_name=constraint['nutrient_name'],
                        min_value=constraint.get('min_value'),
                        max_value=constraint.get('max_value'),
                        actual_value=constraint.get('actual_value')
                    ))

                db.session.commit()

                to_llm_message = [
                    f"System: [Function Result] save_formulation (Status: success)",
                    f"Message: Created new ration '{ration_name}' successfully",
                    f"Saved Ration ID: {new_ration_id}",
                    "Note: New ration created with feeds and constraints."
                ]

                return {
                    'status': 'success',
                    'message': f"Created new ration '{ration_name}' successfully",
                    'ration_id': new_ration_id,
                    'to_llm': "\n".join(to_llm_message)
                }

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error saving formulation: {e}")

            error_message = f"Database error: {str(e)}"
            to_llm_message = [
                f"System: [Function Result] save_formulation (Status: error)",
                f"Message: {error_message}",
                "Error Details:",
                f"  - {str(e)}"
            ]

            return {
                'status': 'error',
                'message': error_message,
                'to_llm': "\n".join(to_llm_message)
            }

    @staticmethod
    def _finish_formulation(context, reason, success=False):
        """Mark the formulation process as complete."""
        if not context:
            error_message = "Context is missing"
            to_llm_message = [
                f"System: [Function Result] finish_formulation (Status: error)",
                f"Message: {error_message}"
            ]

            return {
                'status': 'error',
                'message': error_message,
                'to_llm': "\n".join(to_llm_message)
            }

        # Update context state
        context.is_formulation_complete = True
        context.completion_reason = reason

        to_llm_message = [
            f"System: [Function Result] finish_formulation (Status: success)",
            f"Message: Formulation process completed",
            f"Reason: {reason}",
            f"Successful: {success}",
            "Note: Formulation process marked as complete"
        ]

        return {
            'status': 'success',
            'message': "Formulation process completed",
            'reason': reason,
            'success': success,
            'to_llm': "\n".join(to_llm_message)
        }

    @staticmethod
    def _check_nutrition(context):
        """
        Check nutritional adequacy of given feed inclusions (kg).
        Uses feed data from context.feed_data and context.available_feeds.
        Compares against constraints defined in the context.
        """
        if not context:
            logger.error("_check_nutrition: Context is missing")
            return {'status': 'error', 'message': "Context is missing"}


        # 1. Check currently selected feeds first (context.feed_data)
        if not hasattr(context, 'feed_data') or len(context.feed_data)==0:
            return {'status': 'error', 'message': "No ration defind in context, use make ration tool to add ration.", 'required_action': "add_dmi_constraint"}


        # --- DMI Constraint Check (using context constraints) ---
        dmi_constraint = next((c for c in context.constraints if c.get('nutrient_name') == 'DMI'), None)
        if not dmi_constraint:
            return {'status': 'error', 'message': "DMI constraint is missing from context.", 'required_action': "add_dmi_constraint"}

        target_dmi = dmi_constraint.get('min_value')
        if target_dmi is None: target_dmi = dmi_constraint.get('max_value')
        if target_dmi is None: return {'status': 'error', 'message': "DMI constraint in context has no target value.", 'required_action': "update_dmi_constraint"}
        try: target_dmi = float(target_dmi)
        except (ValueError, TypeError): return {'status': 'error', 'message': "DMI constraint value in context is not a valid number."}

        # --- Calculate Totals using context data ---
        total_kg = sum(feed_dict['inclusion_kg'] for feed_dict in context.feed_data)
        tolerance = min(0.1, target_dmi * 0.01) if target_dmi > 0 else 0.1
        dmi_mismatch = abs(total_kg - target_dmi) > tolerance
        if dmi_mismatch:
             logger.warning(f"_check_nutrition: Total inclusion {total_kg:.2f} kg differs from target DMI {target_dmi:.2f} kg.")

        nutrient_totals = {}
        feeds_details = []
        total_cost_calc = 0.0

        for feed_dict in context.feed_data:
            feed_id = feed_dict['feed_id']
            inclusion_kg = feed_dict['inclusion_kg']
            feed_name = feed_dict.get('name', f"Feed_{feed_id}")
            nutrients_data = feed_dict.get('nutrients') # Get nutrient data from the context dict

            inclusion_percentage = (inclusion_kg / total_kg) * 100 if total_kg > 0 else 0
            cost_per_kg = float(feed_dict.get('cost_per_kg', 0) or 0)
            cost_contribution = inclusion_kg * cost_per_kg
            total_cost_calc += cost_contribution

            feeds_details.append({
                'feed_id': feed_id, 'name': feed_name, 'inclusion_kg': inclusion_kg,
                'inclusion_percentage': inclusion_percentage, 'cost_per_kg': cost_per_kg,
                'cost_contribution': cost_contribution
            })

            # --- Flexible Nutrient Processing (using data from context dict) ---
            processed_nutrient_count = 0

            # Define nutrient categories for special handling
            energy_nutrients = ['NEL', 'NEl', 'ME', 'NEM', 'NEG']
            percentage_nutrients = ['Calcium', 'Phosphorus', 'Magnesium', 'Potassium', 'Sodium', 'Sulfur', 'Chloride', 'Chlorine',
                                   'ADF', 'NDF', 'Crude Protein', 'CP', 'Fat', 'Starch', 'Sugar']

            if isinstance(nutrients_data, list):
                for nutrient_item in nutrients_data:
                    if not isinstance(nutrient_item, dict): continue
                    # Check for both possible key names for nutrient name
                    nutrient_name = nutrient_item.get("nutrient_name") or nutrient_item.get("name")
                    value = nutrient_item.get("value")
                    if nutrient_name and value is not None:
                        try:
                            nutrient_value = float(value)

                            # Initialize if not exists
                            if nutrient_name not in nutrient_totals:
                                nutrient_totals[nutrient_name] = 0.0

                            # Calculate weighted contribution to total
                            if total_kg > 0:
                                nutrient_totals[nutrient_name] += (inclusion_kg / total_kg) * nutrient_value

                            processed_nutrient_count += 1
                        except (ValueError, TypeError):
                            logger.warning(f"Skipping non-numeric list nutrient value '{value}' for {nutrient_name} in feed {feed_name}")
            elif isinstance(nutrients_data, dict):
                 for nutrient_name, value in nutrients_data.items():
                    if value is not None:
                        try:
                            nutrient_value = float(value)

                            # Initialize if not exists
                            if nutrient_name not in nutrient_totals:
                                nutrient_totals[nutrient_name] = 0.0

                            # Calculate weighted contribution to total
                            if total_kg > 0:
                                nutrient_totals[nutrient_name] += (inclusion_kg / total_kg) * nutrient_value

                            processed_nutrient_count += 1
                        except (ValueError, TypeError):
                            logger.warning(f"Skipping non-numeric dict nutrient value '{value}' for {nutrient_name} in feed {feed_name}")
            elif nutrients_data is not None:
                 logger.warning(f"Nutrient data for feed {feed_name} (from context) has unexpected format: {type(nutrients_data)}")

            if processed_nutrient_count == 0 and nutrients_data:
                 logger.warning(f"Feed {feed_name} included but yielded no processable nutrient data from context structure: {nutrients_data}")
                 # Log the first few items to help diagnose the structure
                 if isinstance(nutrients_data, list) and len(nutrients_data) > 0:
                     logger.warning(f"First nutrient item keys: {nutrients_data[0].keys() if isinstance(nutrients_data[0], dict) else 'not a dict'}")
            # --- End Flexible Nutrient Processing ---


        # --- Constraint Comparison (using context constraints) ---
        constraints_report = []
        requirements_met = True
        if 'DMI' not in nutrient_totals: nutrient_totals['DMI'] = total_kg

        for constraint in context.constraints:
            nutrient_name = constraint.get('nutrient_name')
            if not nutrient_name: continue

            min_value = constraint.get('min_value')
            max_value = constraint.get('max_value')
            min_float, max_float = None, None
            try: # Validate constraint values from context
                if min_value is not None: min_float = float(min_value)
                if max_value is not None: max_float = float(max_value)
            except (ValueError, TypeError):
                logger.error(f"Invalid constraint value in context for {nutrient_name}. Min: {min_value}, Max: {max_value}.")
                requirements_met = False
                constraints_report.append({'nutrient_name': nutrient_name, 'min_value': min_value, 'max_value': max_value, 'actual_value': None, 'status': 'error', 'message': 'Invalid constraint value defined in context'})
                continue

            report_item = {'nutrient_name': nutrient_name, 'min_value': min_float, 'max_value': max_float, 'actual_value': None, 'status': 'missing', 'message': 'Nutrient not calculated or not found'}

            if nutrient_name == 'DMI': # Special DMI check report
                 actual_value = total_kg
                 report_item['actual_value'] = actual_value
                 report_item['status'] = 'met' if not dmi_mismatch else 'error'
                 report_item['message'] = f"Target: {target_dmi:.2f}" + ("" if not dmi_mismatch else f", Actual: {total_kg:.2f} - MISMATCH")
                 if dmi_mismatch: requirements_met = False

            elif nutrient_name in nutrient_totals:
                actual_value = nutrient_totals[nutrient_name]
                report_item['actual_value'] = actual_value
                report_item['status'] = 'met'
                report_item['message'] = 'Requirement met'

                # Define nutrient categories for unit handling
                energy_nutrients = ['NEL', 'NEl', 'ME', 'NEM', 'NEG']
                percentage_nutrients = ['Calcium', 'Phosphorus', 'Magnesium', 'Potassium', 'Sodium', 'Sulfur', 'Chloride', 'Chlorine',
                                       'ADF', 'NDF', 'Crude Protein', 'CP', 'Fat', 'Starch', 'Sugar']

                # Check if we need to normalize values for comparison (decimal vs percentage)
                comparison_value = actual_value
                comparison_min = min_float
                comparison_max = max_float

                # Handle percentage vs decimal format mismatch
                if nutrient_name in percentage_nutrients:
                    # If actual is decimal (0-1) but constraints are percentage (0-100)
                    if isinstance(actual_value, (int, float)) and actual_value < 1.0 and (
                        (min_float is not None and min_float > 1.0) or
                        (max_float is not None and max_float > 1.0)
                    ):
                        # Convert actual to percentage for comparison
                        comparison_value = actual_value * 100

                    # If actual is percentage (0-100) but constraints are decimal (0-1)
                    elif isinstance(actual_value, (int, float)) and actual_value > 1.0 and (
                        (min_float is not None and min_float < 1.0 and min_float > 0) or
                        (max_float is not None and max_float < 1.0 and max_float > 0)
                    ):
                        # Convert constraints to percentage for comparison
                        if min_float is not None:
                            comparison_min = min_float * 100
                        if max_float is not None:
                            comparison_max = max_float * 100

                # Check bounds with tolerance
                abs_tol = 0.01
                rel_tol = 0.001

                if comparison_min is not None:
                    min_bound = comparison_min - max(abs_tol, abs(comparison_min * rel_tol))
                    if comparison_value < min_bound:
                        report_item['status'] = 'below_min'
                        report_item['message'] = f'Below minimum of {min_float:.2f}'
                        requirements_met = False

                if comparison_max is not None and report_item['status'] == 'met':
                    max_bound = comparison_max + max(abs_tol, abs(comparison_max * rel_tol))
                    if comparison_value > max_bound:
                        report_item['status'] = 'above_max'
                        report_item['message'] = f'Above maximum of {max_float:.2f}'
                        requirements_met = False
            else:
                # Nutrient required by constraint but not found/calculated
                 report_item['message'] = 'Nutrient required by constraint but not found in feed data'
                 requirements_met = False

            constraints_report.append(report_item)

        # --- Final Result ---
        final_message = 'Nutrition check completed.'
        if not requirements_met: final_message += ' Some requirements were not met.'
        if dmi_mismatch: final_message += ' DMI target mismatch detected.'

        # Create to_llm formatted message for LLM consumption
        to_llm_message = [f"System: [Function Result] check_nutrition (Status: success)", f"Message: {final_message}"]

        # Add nutrition check results
        to_llm_message.append(f"Overall Requirements Met: {requirements_met} {'✅' if requirements_met else '❌'}")
        to_llm_message.append(f"Total Cost: {total_cost_calc:.2f}")
        to_llm_message.append(f"Total kg: {total_kg:.2f} (Target DMI: {target_dmi:.2f})")

        # Add feed inclusions checked
        if feeds_details:
            to_llm_message.append("Feed Inclusions Checked:")
            for feed in feeds_details:
                to_llm_message.append(f"  - {feed.get('name')} (ID: {feed.get('feed_id')})")
                to_llm_message.append(f"    Amount: {feed.get('inclusion_kg', 0):.2f} kg ({feed.get('inclusion_percentage', 0):.2f}%)")
                to_llm_message.append(f"    Cost: {feed.get('cost_contribution', 0):.2f} (at {feed.get('cost_per_kg', 0)} per kg)")

        # Add nutrient results vs constraints
        if constraints_report:
            to_llm_message.append("Nutrient Results vs Constraints:")
            for report in constraints_report:
                nutrient = report.get('nutrient_name', 'Unknown')
                actual = report.get('actual_value')
                status_flag = report.get('status', 'unknown')
                status_emoji = "✅" if status_flag == 'met' else ("❌" if status_flag in ['below_min', 'above_max', 'missing', 'error'] else "❓")

                constraint_range = []
                min_v = report.get('min_value')
                max_v = report.get('max_value')
                if min_v is not None: constraint_range.append(f"Min: {min_v:.2f}")
                if max_v is not None: constraint_range.append(f"Max: {max_v:.2f}")
                range_str = f" ({', '.join(constraint_range)})" if constraint_range else ""

                # Special handling for different nutrient types
                # First, check if we need to convert decimal to percentage
                needs_percentage_conversion = False

                # Energy values (typically in Mcal/kg)
                energy_nutrients = ['NEL', 'NEl', 'ME', 'NEM', 'NEG']
                # Minerals expressed as percentages
                mineral_nutrients = ['Calcium', 'Phosphorus', 'Magnesium', 'Potassium', 'Sodium', 'Sulfur', 'Chloride', 'Chlorine']
                # Major nutrients typically expressed as percentages
                major_nutrients = ['ADF', 'NDF', 'Crude Protein', 'CP', 'Fat', 'Starch', 'Sugar']
                # Trace minerals expressed in mg/kg or ppm
                trace_minerals = ['Iron', 'Manganese', 'Zinc', 'Copper', 'Cobalt', 'Iodine', 'Selenium']

                # Check if we need to convert decimal to percentage for percentage-based nutrients
                if isinstance(actual, (int, float)) and actual < 1.0:
                    if nutrient in mineral_nutrients or nutrient in major_nutrients:
                        # Check if constraint is in percentage (>1.0)
                        if (min_v is not None and min_v > 1.0) or (max_v is not None and max_v > 1.0):
                            needs_percentage_conversion = True

                # Format based on nutrient type
                if nutrient in energy_nutrients:
                    # Energy values are typically small numbers (1-3), display with 2 decimal places
                    actual_str = f"{actual:.2f}" if isinstance(actual, (int, float)) else "N/A" if actual is None else str(actual)
                elif nutrient in mineral_nutrients:
                    # Minerals are typically expressed as percentages
                    if needs_percentage_conversion:
                        actual_str = f"{actual * 100:.2f}" if isinstance(actual, (int, float)) else "N/A" if actual is None else str(actual)
                    else:
                        actual_str = f"{actual:.2f}" if isinstance(actual, (int, float)) else "N/A" if actual is None else str(actual)
                elif nutrient in major_nutrients:
                    # Major nutrients are typically expressed as percentages
                    if needs_percentage_conversion:
                        actual_str = f"{actual * 100:.2f}" if isinstance(actual, (int, float)) else "N/A" if actual is None else str(actual)
                    else:
                        actual_str = f"{actual:.2f}" if isinstance(actual, (int, float)) else "N/A" if actual is None else str(actual)
                elif nutrient in trace_minerals:
                    # Trace minerals are typically in mg/kg (ppm)
                    actual_str = f"{actual:.2f}" if isinstance(actual, (int, float)) else "N/A" if actual is None else str(actual)
                elif nutrient == 'DMI':
                    # DMI is typically a larger number (kg), display with 2 decimal places
                    actual_str = f"{actual:.2f}" if isinstance(actual, (int, float)) else "N/A" if actual is None else str(actual)
                else:
                    # Default formatting for other nutrients
                    # Check if this might be a percentage value that needs conversion
                    if needs_percentage_conversion:
                        actual_str = f"{actual * 100:.2f}" if isinstance(actual, (int, float)) else "N/A" if actual is None else str(actual)
                    else:
                        actual_str = f"{actual:.2f}" if isinstance(actual, (int, float)) else "N/A" if actual is None else str(actual)

                if actual_str == "N/A":
                    to_llm_message.append(f"  - {nutrient}: {actual_str}{range_str} {status_emoji} None of the feeds have {nutrient} data, consider use add_nutrients_to_feed tool or add feed that has this nutrient.")
                else:
                    to_llm_message.append(f"  - {nutrient}: {actual_str}{range_str} {status_emoji}")
        else:
            to_llm_message.append("No constraint report available.")

        result = {
            'status': 'success',
            'requirements_met': requirements_met,
            'message': final_message,
            'feeds': feeds_details, # Feeds included in *this* check
            'nutrient_totals': nutrient_totals, # Calculated profile
            'constraints_report': constraints_report, # Comparison vs context constraints
            'total_cost': total_cost_calc,
            'total_kg': total_kg,
            'target_dmi': target_dmi,
            'to_llm': "\n".join(to_llm_message)
        }
        return result

    @staticmethod
    def _add_nutrients_to_feed(context, feed_id=None, nutrients=None, feeds=None):
        """
        Add or update nutrients for one or multiple feeds in the context.

        Args:
            context: The RationLLMContext instance
            feed_id: (Legacy) The ID of a single feed to update
            nutrients: (Legacy) List of nutrient objects for a single feed
            feeds: List of feed objects, each with feed_id and nutrients list

        Returns:
            Dict with status and message
        """
        if not context:
            return {'status': 'error', 'message': "Context is missing"}

        # Handle both legacy single feed mode and new multiple feeds mode
        feed_updates = []

        # Legacy single feed mode
        if feed_id is not None and nutrients is not None:
            feed_updates.append({'feed_id': feed_id, 'nutrients': nutrients})
        # New multiple feeds mode
        elif feeds is not None and isinstance(feeds, list):
            feed_updates = feeds
        else:
            return {'status': 'error', 'message': "Either feed_id+nutrients or feeds list is required"}

        if not feed_updates:
            return {'status': 'error', 'message': "No valid feed updates provided"}

        # Track overall results
        all_results = {
            'status': 'success',
            'message': "",
            'feed_results': [],
            'to_llm_parts': []
        }

        feeds_processed = 0
        feeds_updated = 0
        feeds_not_found = 0
        feeds_with_errors = 0

        # Process each feed update
        for feed_update in feed_updates:
            update_feed_id = feed_update.get('feed_id')
            update_nutrients = feed_update.get('nutrients')

            if not update_feed_id:
                all_results['feed_results'].append({
                    'status': 'error',
                    'message': "Feed ID is required",
                    'feed_id': None
                })
                feeds_with_errors += 1
                continue

            if not update_nutrients or not isinstance(update_nutrients, list):
                all_results['feed_results'].append({
                    'status': 'error',
                    'message': f"Nutrients must be a non-empty list for feed ID {update_feed_id}",
                    'feed_id': update_feed_id
                })
                feeds_with_errors += 1
                continue

            # Find the feed in the context
            feed_found = False
            for feed in context.feed_data:
                if feed.get('id') == update_feed_id or feed.get('feed_id') == update_feed_id:
                    feed_found = True
                    feeds_processed += 1
                    feed_name = feed.get('name', f"Feed {update_feed_id}")

                    # Initialize nutrients list if it doesn't exist
                    if 'nutrients' not in feed:
                        feed['nutrients'] = []
                    elif not isinstance(feed['nutrients'], list):
                        # Convert dict to list if needed
                        nutrients_dict = feed['nutrients']
                        feed['nutrients'] = []
                        for name, value in nutrients_dict.items():
                            feed['nutrients'].append({
                                'nutrient_name': name,
                                'value': value
                            })

                    # Track which nutrients were added or updated
                    added_nutrients = []
                    updated_nutrients = []

                    # Process each nutrient
                    for nutrient_data in update_nutrients:
                        nutrient_name = nutrient_data.get('name')
                        nutrient_value = nutrient_data.get('value')
                        nutrient_unit = nutrient_data.get('unit', '%')

                        if not nutrient_name or nutrient_value is None:
                            continue

                        # Check if nutrient already exists
                        nutrient_exists = False
                        for existing_nutrient in feed['nutrients']:
                            if (existing_nutrient.get('nutrient_name') == nutrient_name or
                                existing_nutrient.get('name') == nutrient_name):
                                # Update existing nutrient
                                existing_nutrient['value'] = nutrient_value
                                if 'unit' not in existing_nutrient and nutrient_unit:
                                    existing_nutrient['unit'] = nutrient_unit
                                nutrient_exists = True
                                updated_nutrients.append(nutrient_name)
                                break

                        # Add new nutrient if it doesn't exist
                        if not nutrient_exists:
                            feed['nutrients'].append({
                                'nutrient_name': nutrient_name,
                                'value': nutrient_value,
                                'unit': nutrient_unit
                            })
                            added_nutrients.append(nutrient_name)

                    # Prepare feed result
                    feed_result = {
                        'feed_id': update_feed_id,
                        'feed_name': feed_name,
                        'added_nutrients': added_nutrients,
                        'updated_nutrients': updated_nutrients
                    }

                    if added_nutrients or updated_nutrients:
                        feed_result['status'] = 'success'
                        feeds_updated += 1

                        message_parts = []
                        if added_nutrients:
                            message_parts.append(f"Added {len(added_nutrients)} nutrients to {feed_name}")
                        if updated_nutrients:
                            message_parts.append(f"Updated {len(updated_nutrients)} nutrients in {feed_name}")
                        feed_result['message'] = ". ".join(message_parts)
                    else:
                        feed_result['status'] = 'warning'
                        feed_result['message'] = f"No nutrients were added or updated for {feed_name}"

                    all_results['feed_results'].append(feed_result)

                    # Add to LLM message parts
                    all_results['to_llm_parts'].append(f"Feed: {feed_name} (ID: {update_feed_id})")
                    if added_nutrients:
                        all_results['to_llm_parts'].append(f"  Added nutrients:")
                        for name in added_nutrients:
                            all_results['to_llm_parts'].append(f"    - {name}")
                    if updated_nutrients:
                        all_results['to_llm_parts'].append(f"  Updated nutrients:")
                        for name in updated_nutrients:
                            all_results['to_llm_parts'].append(f"    - {name}")

                    break

            if not feed_found:
                feeds_not_found += 1
                all_results['feed_results'].append({
                    'status': 'error',
                    'message': f"Feed with ID {update_feed_id} not found in the current ration",
                    'feed_id': update_feed_id
                })
                all_results['to_llm_parts'].append(f"Feed ID {update_feed_id}: Not found in the current ration")

        # Determine overall status and message
        if feeds_updated > 0:
            if feeds_not_found > 0 or feeds_with_errors > 0:
                all_results['status'] = 'partial'
            else:
                all_results['status'] = 'success'
        else:
            if feeds_not_found > 0:
                all_results['status'] = 'error'
            else:
                all_results['status'] = 'warning'

        # Create summary message
        message_parts = []
        if feeds_updated > 0:
            message_parts.append(f"Updated nutrients for {feeds_updated} feed(s)")
        if feeds_not_found > 0:
            message_parts.append(f"Could not find {feeds_not_found} feed(s) make sure you have added feed to the ration.")
        if feeds_with_errors > 0:
            message_parts.append(f"Encountered errors with {feeds_with_errors} feed update(s)")

        all_results['message'] = ". ".join(message_parts)

        # Create to_llm formatted message
        to_llm_message = [
            f"System: [Function Result] add_nutrients_to_feed (Status: {all_results['status']})",
            f"Message: {all_results['message']}"
        ]

        # Add feed-specific details
        if all_results['to_llm_parts']:
            to_llm_message.append("Feed Updates:")
            to_llm_message.extend(all_results['to_llm_parts'])

        all_results['to_llm'] = "\n".join(to_llm_message)

        return all_results

    @staticmethod
    def _make_ration(context, args):
        """
        Save an LLM-created ration formulation using kg inclusions.
        Handles different nutrient data formats (list of dicts or dict).
        Updates the context with the result.
        """
        if not context:
            return {'status': 'error', 'message': "Context is missing"}

        # --- DMI Constraint Check (using context constraints) ---
        dmi_constraint = next((c for c in context.constraints if c.get('nutrient_name') == 'DMI'), None)
        if not dmi_constraint:
            return {'status': 'error', 'message': "DMI constraint is missing from context.", 'required_action': "add_dmi_constraint"}

        target_dmi = dmi_constraint.get('min_value')
        if target_dmi is None: target_dmi = dmi_constraint.get('max_value')
        if target_dmi is None:
            return {'status': 'error', 'message': "DMI constraint in context has no target value.", 'required_action': "update_dmi_constraint"}
        try:
            target_dmi = float(target_dmi)
        except (ValueError, TypeError):
            return {'status': 'error', 'message': "DMI constraint value in context is not a valid number."}

        # --- Process Feed Inclusions ---
        feed_inclusions = args.get('feed_inclusions', [])
        if not feed_inclusions:
            return {'status': 'error', 'message': "Feed inclusions are required for ration formulation"}

        # Validate inclusions
        validated_inclusions = []
        validated_ids = []
        total_kg = 0.0
        for i, inclusion in enumerate(feed_inclusions):
            if 'feed_id' not in inclusion or 'inclusion_kg' not in inclusion:
                return {'status': 'error', 'message': f"Inclusion {i+1} must have feed_id and inclusion_kg"}
            try:
                feed_id = int(inclusion['feed_id'])
                inclusion_kg = float(inclusion['inclusion_kg'])
                if inclusion_kg < 0:
                    return {'status': 'error', 'message': f"Inclusion amount for feed {feed_id} must be non-negative"}
                validated_inclusions.append({'feed_id': feed_id, 'inclusion_kg': inclusion_kg})
                validated_ids.append(feed_id)
                total_kg += inclusion_kg
            except (ValueError, TypeError):
                return {'status': 'error', 'message': f"Invalid feed_id or inclusion_kg in inclusion {i+1}"}

        # Check total against DMI target
        tolerance = min(0.1, target_dmi * 0.01) if target_dmi > 0 else 0.1
        if abs(total_kg - target_dmi) > tolerance:
            # Strict check for make_ration: must match DMI to save as formulation result
            return {'status': 'error', 'message': f"Total inclusion ({total_kg:.2f} kg) must match target DMI ({target_dmi:.2f} kg). Formulation not saved to context."}

        # --- Build feed map from context ---
        feed_map = {}
        current_feed_ids = []
        if hasattr(context, 'feed_data') and isinstance(context.feed_data, list):
            for feed in context.feed_data:
                feed_id = feed.get('id') or feed.get('feed_id')
                if feed_id:
                    int_feed_id = int(feed_id)
                    feed_map[int_feed_id] = feed
                    current_feed_ids.append(int_feed_id)

        # If feeds in the request don't exist in context or available_feeds, fetch from DB if possible
        missing_feeds = [inc['feed_id'] for inc in validated_inclusions if int(inc['feed_id']) not in feed_map]
        if missing_feeds:
            # Load from available_feeds if not in feed_data
            if not hasattr(context, 'available_feeds') or not context.available_feeds:
                context.load_available_feeds()

            # Update feed_map with available_feeds
            for feed in context.available_feeds:
                feed_id = feed.get('id') or feed.get('feed_id')
                if feed_id and int(feed_id) in missing_feeds and int(feed_id) not in feed_map:
                    feed_map[int(feed_id)] = feed

            # Check if we still have missing feeds
            still_missing = [feed_id for feed_id in missing_feeds if int(feed_id) not in feed_map]
            if still_missing:
                return {'status': 'error', 'message': f"Cannot find feed information for IDs: {still_missing}. Use add_feed_to_ration first."}

        # --- Track feed changes ---
        adjusted_feeds = []
        added_feeds = []
        removed_feeds = []

        # --- Update context with feed changes ---
        # 1. Update feeds with new inclusion values
        for inclusion in validated_inclusions:
            feed_id = int(inclusion['feed_id'])
            feed_detail = feed_map[feed_id]
            inclusion_kg = inclusion['inclusion_kg']

            # Determine min/max inclusion percentages (use existing if available)
            min_incl = 0
            max_incl = 100
            existing_feed = next((f for f in context.feed_data if int(f.get('id', f.get('feed_id'))) == feed_id), None)
            if existing_feed:
                min_incl = existing_feed.get('min_inclusion_percentage', min_incl)
                max_incl = existing_feed.get('max_inclusion_percentage', max_incl)
                adjusted_feeds.append(feed_id)
            else:
                added_feeds.append(feed_id)

            # Add or update feed with inclusion_kg
            context.add_feed(
                feed_id=feed_id,
                feed_detail=feed_detail,
                min_inclusion=min_incl,
                max_inclusion=max_incl,
                inclusion_kg=inclusion_kg
            )

        # 2. Remove feeds not in the validated list
        for feed_id in current_feed_ids:
            if feed_id not in validated_ids:
                context.remove_feed(feed_id)
                removed_feeds.append(feed_id)

        # --- Create change summary ---
        feed_changes = {
            'added': [{'id': feed_id, 'name': feed_map[feed_id].get('name', f"Feed_{feed_id}")} for feed_id in added_feeds],
            'adjusted': [{'id': feed_id, 'name': feed_map[feed_id].get('name', f"Feed_{feed_id}")} for feed_id in adjusted_feeds],
            'removed': [{'id': feed_id, 'name': feed_map.get(feed_id, {}).get('name', f"Feed_{feed_id}")} for feed_id in removed_feeds]
        }

        # --- Calculate nutrition totals directly (simplified version) ---
        nutrient_totals = {}
        feeds_result_dict = {}
        total_cost_calc = 0.0

        for feed in context.feed_data:
            feed_id = feed.get('id') or feed.get('feed_id')
            if not feed_id:
                continue

            feed_name = feed.get('name', f"Feed_{feed_id}")
            inclusion_kg = feed.get('inclusion_kg', 0)

            # Calculate percentage and cost
            inclusion_percentage = (inclusion_kg / total_kg) * 100 if total_kg > 0 else 0
            cost_per_kg = float(feed.get('cost_per_kg', 0) or 0)
            cost_contribution = inclusion_kg * cost_per_kg
            total_cost_calc += cost_contribution

            # Add to results dict
            feeds_result_dict[str(feed_id)] = {
                'name': feed_name,
                'inclusion_percentage': inclusion_percentage,
                'cost_contribution': cost_contribution,
                'inclusion_kg': inclusion_kg
            }

            # Calculate nutrient totals (basic approach)
            nutrients_data = feed.get('nutrients')
            if isinstance(nutrients_data, list):
                for nutrient_item in nutrients_data:
                    nutrient_name = nutrient_item.get("nutrient_name") or nutrient_item.get("name")
                    value = nutrient_item.get("value")
                    if nutrient_name and value is not None:
                        try:
                            nutrient_value = float(value)
                            if nutrient_name not in nutrient_totals:
                                nutrient_totals[nutrient_name] = 0.0
                            if total_kg > 0:
                                nutrient_totals[nutrient_name] += (inclusion_kg / total_kg) * nutrient_value
                        except (ValueError, TypeError):
                            pass
            elif isinstance(nutrients_data, dict):
                for nutrient_name, value in nutrients_data.items():
                    if value is not None:
                        try:
                            nutrient_value = float(value)
                            if nutrient_name not in nutrient_totals:
                                nutrient_totals[nutrient_name] = 0.0
                            if total_kg > 0:
                                nutrient_totals[nutrient_name] += (inclusion_kg / total_kg) * nutrient_value
                        except (ValueError, TypeError):
                            pass

        # Add DMI to nutrient totals if not present
        if 'DMI' not in nutrient_totals:
            nutrient_totals['DMI'] = total_kg

        # --- Format nutrients for formulation result ---
        nutrients_result_dict = {}
        for nutrient_name, actual_value in nutrient_totals.items():
            constraint = next((c for c in context.constraints if c.get('nutrient_name') == nutrient_name), None)
            min_val = constraint.get('min_value') if constraint else None
            max_val = constraint.get('max_value') if constraint else None
            min_float, max_float = None, None
            try:
                if min_val is not None: min_float = float(min_val)
                if max_val is not None: max_float = float(max_val)
            except (ValueError, TypeError):
                pass

            nutrients_result_dict[nutrient_name] = {
                'actual_value': actual_value,
                'min_value': min_float,
                'max_value': max_float
            }

        # --- Build formulation result ---
        formulation_result = {
            'status': 'success',
            'message': 'LLM-based formulation processed successfully.',
            'feeds': feeds_result_dict,
            'nutrients': nutrients_result_dict,
            'actual_total_cost': total_cost_calc,
            'cost_per_kg_dm': total_cost_calc / target_dmi if target_dmi > 0 else 0,
            'source': 'llm'
        }

        # Update context with this new formulation result
        context.update_formulation_result(formulation_result)
        context.formulation_attempts += 1

        # --- Create detailed LLM response ---
        to_llm_message = [
            f"System: [Function Result] make_ration (Status: success)",
            f"Message: Ration formulation created and saved to context successfully."
        ]

        # Add feed change summary
        feed_change_parts = []
        if added_feeds:
            feed_names = ", ".join([feed_map[feed_id].get('name', f"Feed_{feed_id}") for feed_id in added_feeds])
            feed_change_parts.append(f"Added: {feed_names}")
        if adjusted_feeds:
            feed_names = ", ".join([feed_map[feed_id].get('name', f"Feed_{feed_id}") for feed_id in adjusted_feeds])
            feed_change_parts.append(f"Updated: {feed_names}")
        if removed_feeds:
            feed_names = ", ".join([feed_map.get(feed_id, {}).get('name', f"Feed_{feed_id}") for feed_id in removed_feeds])
            feed_change_parts.append(f"Removed: {feed_names}")

        if feed_change_parts:
            to_llm_message.append("Feed Changes:")
            for part in feed_change_parts:
                to_llm_message.append(f"  - {part}")

        # Add basic ration information
        to_llm_message.append(f"\nTotal Cost: {total_cost_calc:.2f}")
        to_llm_message.append(f"Total kg: {total_kg:.2f} (Target DMI: {target_dmi:.2f})")

        # Add feed inclusions
        to_llm_message.append("\nFeed Inclusions:")
        for feed in context.feed_data:
            feed_id = feed.get('id') or feed.get('feed_id')
            feed_name = feed.get('name', f"Feed_{feed_id}")
            inclusion_kg = feed.get('inclusion_kg', 0)
            inclusion_percentage = (inclusion_kg / total_kg) * 100 if total_kg > 0 else 0
            cost_per_kg = float(feed.get('cost_per_kg', 0) or 0)
            cost_contribution = inclusion_kg * cost_per_kg

            to_llm_message.append(f"  - {feed_name} (ID: {feed_id})")
            to_llm_message.append(f"    Amount: {inclusion_kg:.2f} kg ({inclusion_percentage:.2f}%)")
            to_llm_message.append(f"    Cost: {cost_contribution:.2f} (at {cost_per_kg} per kg)")

        # Add note about checking nutrition
        to_llm_message.append("\nNote: Use check_nutrition tool to verify nutritional adequacy.")

        return {
            'status': 'success',
            'message': 'Ration formulation created and saved to context successfully.',
            'feed_changes': feed_changes,
            'total_cost': total_cost_calc,
            'total_kg': total_kg,
            'target_dmi': target_dmi,
            'result': formulation_result,
            'to_llm': "\n".join(to_llm_message)
        }
