# Updated backend/app/routes/ration_agent.py

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
# We need RationLLMContext again for the /init endpoint
from ..services.ration_llm_context import RationLLMContext
from ..services.ration_agent_service import RationAgentService
import json
import logging

logger = logging.getLogger(__name__)

bp = Blueprint('ration_agent', __name__, url_prefix='/api/ration-agent')

@bp.route('/<int:ration_id>/init', methods=['GET'])
@jwt_required()
def initialize_agent(ration_id):
    """Initialize agent for existing ration"""
    current_user_id = get_jwt_identity()
    locale = request.args.get('locale', 'en')
    
    try:
        # Replace removed load_ration_data() with direct DB query
        from ..models import Ration, RationFeed, Feed, Constraint
        from ..extensions import db
        
        # Get ration with relationships
        ration = Ration.query.filter_by(id=ration_id, user_id=current_user_id).first()
        if not ration:
            return jsonify({"status": "error", "message": f"Ration {ration_id} not found"}), 404
            
        # Create context manually
        context = RationLLMContext(current_user_id, ration_id, locale)
        
        # Populate context attributes from DB data
        context.current_formulation_state = {
            "ration_name": ration.name,
            "description": ration.description,
            "animal_group_id": ration.animal_group_id,
            "feeds_count": len(ration.ration_feeds),
            "constraints_count": len(ration.constraints),
            "last_formulated_at": ration.last_formulated_at.isoformat() if ration.last_formulated_at else None
        }
        
        # Fetch feed details
        context.feed_data = []
        for rf in ration.ration_feeds:
            feed = rf.feed
            feed_data = {
                "id": feed.id,
                "feed_id": feed.id,
                "name": feed.name,
                "min_inclusion_percentage": rf.min_inclusion_percentage,
                "max_inclusion_percentage": rf.max_inclusion_percentage,
                "actual_inclusion_percentage": rf.actual_inclusion_percentage,
                "cost_contribution": rf.cost_contribution,
                "dry_matter_percentage": feed.dry_matter_percentage,
                "cost_per_kg": feed.cost_per_kg,
                "nutrients": {n.nutrient.name_en: n.value for n in feed.nutrients if n.nutrient}
            }
            context.feed_data.append(feed_data)
            
        # Add constraints
        context.constraints = [
            {
                "nutrient_name": c.nutrient_name,
                "min_value": c.min_value,
                "max_value": c.max_value,
                "actual_value": c.actual_value
            } for c in ration.constraints
        ]
        
        # Get animal group data if needed
        if ration.animal_group:
            context.herd_info = ration.animal_group.to_dict()
            
        return jsonify({
            "status": "success", 
            "message": "Agent initialized with ration data",
            "context": context.to_dict()
        }), 200
        
    except Exception as e:
        logger.exception(f"Error initializing agent: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500


@bp.route('/chat', methods=['POST'])
@jwt_required()
def chat_with_agent():
    """
    Handles chat interaction. Expects 'context_data' containing the
    current state (including history) from the frontend overlay.
    Initializes context based on this data for each turn.
    """
    current_user_id = get_jwt_identity()
    data = request.get_json()

    if not data:
        logger.warning(f"User {current_user_id}: Received empty request body for /chat.")
        return jsonify({"status": "error", "message": "Request body cannot be empty."}), 400

    message = data.get('message')
    context_data = data.get('context_data') # EXPECTING this from frontend overlay state
    # ration_id might be null in context_data for new rations, that's okay
    ration_id = context_data.get('ration_id') if context_data else None
    locale = data.get('locale', 'en')

    if not message:
        logger.warning(f"User {current_user_id}: Received request without 'message' for /chat.")
        return jsonify({"status": "error", "message": "'message' is required."}), 400

    if not context_data or not isinstance(context_data, dict):
        logger.error(f"User {current_user_id}: Received request without valid 'context_data' for /chat.")
        return jsonify({"status": "error", "message": "Valid 'context_data' is required for chat."}), 400

    logger.info(f"User {current_user_id}: Processing chat message for ration ID: {ration_id}. Locale: {locale}.")
    logger.debug(f"User {current_user_id}: Received context_data keys: {list(context_data.keys())}")

    try:
        # The service now expects context_data to initialize its state for the turn
        response_data = RationAgentService.process_message(
            user_id=current_user_id,
            message=message,
            context_data=context_data, # Pass the received context
            ration_id=ration_id,       # Pass ID if available in context_data
            locale=locale
        )
        # Return the updated context state after processing
        return jsonify(response_data), 200

    except ValueError as ve:
         logger.error(f"User {current_user_id}: Value error processing message: {ve}")
         return jsonify({"status": "error", "message": str(ve)}), 400
    except Exception as e:
         logger.exception(f"User {current_user_id}: Unexpected error in /chat endpoint: {e}")
         return jsonify({"status": "error", "message": "An internal server error occurred."}), 500

# Removed /analyze endpoint - functionality moved to tools via /chat