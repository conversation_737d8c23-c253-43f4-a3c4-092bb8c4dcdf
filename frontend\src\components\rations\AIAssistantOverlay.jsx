// src/components/rations/AIAssistantOverlay.jsx
import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import i18n from 'i18next';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { nord } from 'react-syntax-highlighter/dist/esm/styles/prism';
import Button from '../ui/Button';
import Card from '../ui/Card';
import api from '../../services/api';
import { formatNumber, formatPrice } from '../../utils/formatters';
import '../../../src/styles/markdown.css';

/**
 * AIAssistantOverlay - A stateless AI assistant that works with the ration formulation service
 * Now using a context-based architecture where this component uses the parent's context
 *
 * @param {Object} props Component props
 * @param {Object} props.triggerData Data object that activates the overlay and provides initial context
 *   - message: Initial message to send to the AI
 *   - rationId: ID of the existing ration (null for new rations)
 *   - context: Initial context data for new rations (null for existing rations)
 * @param {Object} props.currentContext Current context data from the parent component
 * @param {Array} props.availableFeeds List of all available feeds for display
 * @param {Function} props.onClose Callback to close the overlay
 * @param {Function} props.onContextChange Callback to notify parent of context changes
 */
const AIAssistantOverlay = ({
  triggerData,
  currentContext, // Receive the shared context instead of formData
  availableFeeds,
  onClose,
  onContextChange // This will directly update the parent's context
}) => {
  const { t } = useTranslation();
  const messagesEndRef = useRef(null);

  // Determine if overlay should be active based on triggerData
  const isActive = !!triggerData;

  // State
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [madeChanges, setMadeChanges] = useState(false);
  const [activeTab, setActiveTab] = useState('conversation');
  const [aiActions, setAiActions] = useState([]);
  const [beforeState, setBeforeState] = useState(null);
  const [recommendations, setRecommendations] = useState([]);
  const [initialQuerySent, setInitialQuerySent] = useState(false);
  const [isAutoRunning, setIsAutoRunning] = useState(false);
  const [currentStatus, setCurrentStatus] = useState('');
  const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle overlay activation and initialization
  useEffect(() => {
    // Reset state when overlay is closed
    if (!isActive) {
      setInitialQuerySent(false);
      return;
    }

    // Skip initialization if we already have context and initialization wasn't retriggered
    if (currentContext && initialQuerySent) {
      return;
    }

    console.log(`[AIAssistantOverlay] Initializing. TriggerData:`, triggerData);

    // Start initialization process
    setIsLoading(true);

    // Reset all UI state for new session
    setMessages([]);
    setInput('');
    setAiActions([]);
    setRecommendations([]);
    setMadeChanges(false);
    setActiveTab('conversation');
    setInitialQuerySent(false);

    // Always add welcome message
    const welcomeMsg = {
      role: 'assistant',
      content: t('rations.aiWelcomeMessage'),
      timestamp: new Date().toISOString()
    };
    setMessages([welcomeMsg]);

    // Set the initial context (will be used for comparison in the Changes tab)
    if (currentContext) {
      setBeforeState(JSON.parse(JSON.stringify(currentContext)));
    }

    // Send initial message if provided
    if (triggerData?.message && !initialQuerySent) {
      console.log('[AIAssistantOverlay] Sending initial message:', triggerData.message);

      // Check if this is the auto-proceed message (used as a placeholder)
      const isAutoProceedMessage = triggerData.message === t('rations.statusUpdates.continue');

      // Check if initial message contains formulation keywords
      const formKeywords = ['formulate', 'optimize', 'create ration', 'make ration', 'start formulation'];
      const shouldAutoFormulate = !isAutoProceedMessage && formKeywords.some(keyword =>
        triggerData.message.toLowerCase().includes(keyword.toLowerCase())
      );

      if (shouldAutoFormulate) {
        setIsAutoRunning(true);
      }

      // For auto-proceed messages, don't show them in the UI
      if (!isAutoProceedMessage) {
        // Add user message to UI for non-auto-proceed messages
        const userMessage = {
          role: 'user',
          content: triggerData.message.trim(),
          timestamp: new Date().toISOString()
        };
        setMessages(prev => [...prev, userMessage]);
      }

      // Always send the message to backend
      const contextToUse = triggerData.context || currentContext;
      sendMessageToBackend(triggerData.message, contextToUse, isAutoProceedMessage);
      setInitialQuerySent(true);
    } else {
      // If no initial message, just mark as initialized
      setInitialQuerySent(true);
      setIsLoading(false);
    }
  }, [triggerData, isActive, t, currentContext]);

  // Send message to backend AI service
  const sendMessageToBackend = async (messageText, contextToUse = null, isAutoContinue = false) => {
    if (!messageText || (!contextToUse && !currentContext)) {
      console.warn('[AIAssistantOverlay] Cannot send message: missing message or context');
      return;
    }

    // Use provided context or current state
    const contextForApi = contextToUse || currentContext;

    // Create user message (only if not auto-continue and not already added)
    // We skip this for auto-continue messages and for messages that were already added in the initialization
    let userMessage = null;
    if (!isAutoContinue && messageText !== t('rations.statusUpdates.continue')) {
      // Check if this message is already in the messages array
      const messageExists = messages.some(msg =>
        msg.role === 'user' && msg.content === messageText.trim()
      );

      if (!messageExists && messageText.toLowerCase().trim() !== "stop") {
        userMessage = {
          role: 'user',
          content: messageText.trim(),
          timestamp: new Date().toISOString()
        };

        // Update UI with user message - but only if not called from sendMessage()
        // since sendMessage() already adds the message to UI
        if (!contextToUse || !contextToUse.message_history ||
            !contextToUse.message_history.some(msg =>
              msg.role === 'user' && msg.content === messageText.trim()
            )) {
          setMessages(prev => [...prev, userMessage]);
        }
      }
    }

    setInput('');
    setIsLoading(true);
    setIsWaitingForResponse(true);

    try {
      // Create a context object with the auto_continue flag and updated message history
      const contextWithFlags = {
        ...contextForApi,
        auto_continue: isAutoContinue
      };

      // Add the new user message to the message_history in the context
      // This ensures the backend has the complete conversation history
      if (userMessage && messageText.toLowerCase().trim() !== "stop") {
        // Only add to message_history if it's not already there
        const messageHistoryExists = contextWithFlags.message_history?.some(msg =>
          msg.role === 'user' && msg.content === messageText.trim()
        );

        if (!messageHistoryExists) {
          // Initialize message_history if it doesn't exist
          if (!contextWithFlags.message_history) {
            contextWithFlags.message_history = [];
          }

          // Add the user message to the context's message history
          contextWithFlags.message_history.push(userMessage);
        }
      }

      console.log('[AIAssistantOverlay] Sending message to backend:', {
        message: messageText,
        context_data: contextWithFlags,
        auto_continue: isAutoContinue
      });

      console.log('[AIAssistantOverlay] Context with flags:', {
        auto_continue: contextWithFlags.auto_continue,
        message: messageText,
        message_history_length: contextWithFlags.message_history?.length || 0
      });

      const response = await api.post('/ration-agent/chat', {
        message: messageText,
        context_data: contextWithFlags,
        ration_id: contextWithFlags.ration_id,
        locale: i18n.language
      });

      console.log('[AIAssistantOverlay] Received response:', response.data);

      if (response.data.context) {
        // Directly notify parent of context change
        if (onContextChange) {
          console.log('[AIAssistantOverlay] Notifying parent of context change');
          onContextChange(response.data.context);
        }

        // Update status if provided
        if (response.data.status) {
          console.log(`[AIAssistantOverlay] Setting status: ${response.data.status}`);
          setCurrentStatus(response.data.status);
        }

        // Set auto-running state based on auto_continue flag
        console.log(`[AIAssistantOverlay] Auto-continue flag: ${response.data.auto_continue}`);
        if (response.data.auto_continue) {
          console.log('[AIAssistantOverlay] Setting isAutoRunning to true');
          setIsAutoRunning(true);
        }

        // Handle assistant message and thinking content
        if (response.data.assistant_message && response.data.assistant_message.trim()) {
          const originalMessage = {
            role: 'assistant',
            content: response.data.assistant_message,
            timestamp: new Date().toISOString()
          };

          setMessages(prev => [...prev, originalMessage]);
          extractRecommendations(response.data.assistant_message);
        }

        // Add thinking content as a separate message if available
        if (response.data.thinking_content && response.data.thinking_content.trim()) {
          const thinkingMessage = {
            role: 'assistant',
            content: `[Thinking]: ${response.data.thinking_content}`,
            timestamp: new Date().toISOString(),
            isThinking: true
          };

          setMessages(prev => [...prev, thinkingMessage]);
        }

        // Process function call if any
        if (response.data.function_used) {
          const functionName = response.data.function_used;
          const functionResult = response.data.function_result || {};

          console.log(`[AIAssistantOverlay] Function used: ${functionName}`, functionResult);

          // Add system message for function call
          const systemMessage = {
            role: 'system',
            content: '',
            functionInfo: {
              name: functionName,
              result: functionResult
            },
            timestamp: new Date().toISOString()
          };

          setMessages(prev => [...prev, systemMessage]);

          // Add to action history
          const actionTime = new Date().toISOString();
          setAiActions(prev => [
            ...prev,
            {
              name: functionName,
              description: formatActionName(functionName),
              result: functionResult,
              timestamp: actionTime
            }
          ]);

          // Flag changes for certain functions
          const changeFunctions = [
            'add_feed_to_ration',
            'remove_feed_from_ration',
            'update_nutrient_constraint',
            'run_formulation',
            'save_formulation',
            'check_nutrition',
            'make_ration',
            'add_nutrients_to_feed'
          ];

          if (changeFunctions.includes(functionName)) {
            setMadeChanges(true);

            // Extract recommendations from conflicts if formulation was run
            if (functionName === 'run_formulation' && functionResult?.conflicts) {
              extractRecommendationsFromConflicts(functionResult.conflicts);
            }
          }
        }

        // Add assistant's text response only if it's not a duplicate of the thinking content
        // or if it contains additional information beyond the function status
        if (response.data.response &&
            response.data.response !== response.data.assistant_message &&
            !response.data.response.includes(response.data.thinking_content) &&
            !response.data.response.includes("[Function]:")) {
          // This becomes a system message, not an assistant message
          const statusMessage = {
            role: 'system',
            content: response.data.response,
            timestamp: new Date().toISOString()
          };

          if (!isAutoContinue || response.data.response.length > 30) {
            setMessages(prev => [...prev, statusMessage]);
          }
        }

        // If process is stopped, update UI
        if (response.data.stopped) {
          console.log('[AIAssistantOverlay] Process stopped by backend');
          console.log('[AIAssistantOverlay] Context data:', response.data.context);

          // Explicitly set auto-running to false
          setIsAutoRunning(false);
          setIsLoading(false); // Ensure loading state is reset
          setCurrentStatus(t('rations.statusUpdates.stopped'));

          // Add system message about stopping if not already added
          const stopMessageExists = messages.some(msg =>
            msg.role === 'system' &&
            (msg.content === t('rations.statusUpdates.formulationStopped') ||
             msg.content === t('rations.statusUpdates.stoppingFormulation', 'Stopping formulation...'))
          );

          if (!stopMessageExists) {
            const stoppedMessage = {
              role: 'system',
              content: t('rations.statusUpdates.formulationStopped'),
              timestamp: new Date().toISOString()
            };

            setMessages(prev => [...prev, stoppedMessage]);
          }

          // Update the context to ensure it has the stopped flags
          if (response.data.context && onContextChange) {
            const updatedContext = {
              ...response.data.context,
              is_formulation_complete: true,
              completion_reason: "Stopped by user",
              auto_continue: false,
              stopped: true
            };
            onContextChange(updatedContext);
          }

          // Log the state after update
          console.log('[AIAssistantOverlay] Auto-running disabled due to stop command');
        }
      } else {
        console.warn('[AIAssistantOverlay] Response missing context data');

        // Add assistant response even if context is missing
        const assistantMessage = {
          role: 'assistant',
          content: response.data.response || "[Error: Backend did not return context]",
          timestamp: new Date().toISOString()
        };

        setMessages(prev => [...prev, assistantMessage]);
      }
    } catch (error) {
      console.error('[AIAssistantOverlay] Error sending message:', error);

      // Add error message
      const errorMessage = {
        role: 'system',
        content: t('rations.aiCommunicationError'),
        error: true,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, errorMessage]);

      // Stop auto-running on error
      setIsAutoRunning(false);
    } finally {
      setIsLoading(false);
      setIsWaitingForResponse(false);
    }
  };

  // Add auto-continue effect
  useEffect(() => {
    // Extract auto_continue flag from current context
    const autoFlag = currentContext?.auto_continue === true;
    const notWaiting = !isWaitingForResponse;
    const isRunning = isAutoRunning;
    const notCompleted = !(currentContext?.is_formulation_complete === true);
    const isStopped = currentContext?.completion_reason === "Stopped by user";

    console.log('[AIAssistantOverlay] Auto-continue check:', {
      auto_continue: autoFlag,
      isWaitingForResponse: !notWaiting,
      isAutoRunning: isRunning,
      isComplete: !notCompleted,
      isStopped: isStopped,
      contextPresent: !!currentContext
    });

    // If the process was explicitly stopped by the user, ensure auto-running is disabled
    if (isStopped) {
      console.log('[AIAssistantOverlay] Process was stopped by user (completion_reason="Stopped by user"), disabling auto-running');
      console.log('[AIAssistantOverlay] Current context:', currentContext);
      setIsAutoRunning(false);
      setCurrentStatus('');
      return;
    }

    // If backend explicitly set stopped=true in the response, ensure auto-running is disabled
    if (currentContext?.stopped === true) {
      console.log('[AIAssistantOverlay] Process was stopped by backend (stopped=true), disabling auto-running');
      setIsAutoRunning(false);
      setCurrentStatus('');
      return;
    }

    // Normal auto-continue logic
    if (autoFlag && notWaiting && isRunning && notCompleted && !isStopped) {
      console.log('[AIAssistantOverlay] Auto-continuing formulation process');
      const timer = setTimeout(() => {
        // Double-check that we're still in auto-running mode before sending
        // This prevents a race condition where stop was clicked during the timeout
        if (isAutoRunning && !isStopped) {
          console.log('[AIAssistantOverlay] Sending continuation message');
          sendMessageToBackend(t('rations.statusUpdates.continue'), currentContext, true);
        } else {
          console.log('[AIAssistantOverlay] Auto-continue cancelled - no longer in auto-running mode');
        }
      }, 1000);

      return () => clearTimeout(timer);
    }

    // Stop auto-running if limits reached or complete
    if (!notCompleted) {
      setIsAutoRunning(false);
      setCurrentStatus('');
    }
  }, [currentContext, isWaitingForResponse, isAutoRunning, t]);

  // Handle user sending a message
  const sendMessage = () => {
    if (!input.trim() || !currentContext) {
      return;
    }

    // Start auto-formulation if the message contains formulation keywords
    const formKeywords = ['formulate', 'optimize', 'create ration', 'make ration', 'start formulation'];
    const shouldAutoFormulate = formKeywords.some(keyword =>
      input.toLowerCase().includes(keyword.toLowerCase())
    );

    if (shouldAutoFormulate) {
      setIsAutoRunning(true);
    }

    // Create a user message object
    const userMessage = {
      role: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString()
    };

    // Add to UI messages
    setMessages(prev => [...prev, userMessage]);

    // Create updated context with the new message in message_history
    const updatedContext = {
      ...currentContext
    };

    // Initialize message_history if it doesn't exist
    if (!updatedContext.message_history) {
      updatedContext.message_history = [];
    }

    // Add the user message to the context's message history
    updatedContext.message_history.push(userMessage);

    // Send the message with the updated context
    sendMessageToBackend(input, updatedContext);
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Extract recommendations from AI response text
  const extractRecommendations = (text) => {
    // Simple regex-based recommendation extraction
    // Look for patterns like "I recommend...", "You should...", etc.
    const recommendationPatterns = [
      /I recommend (.*?)(?:\.|$)/gi,
      /you should (.*?)(?:\.|$)/gi,
      /it's best to (.*?)(?:\.|$)/gi,
      /I suggest (.*?)(?:\.|$)/gi
    ];

    const newRecommendations = [];

    recommendationPatterns.forEach(pattern => {
      const matches = [...text.matchAll(pattern)];
      matches.forEach(match => {
        if (match[1] && match[1].length > 10) {
          newRecommendations.push({
            text: match[1].trim(),
            source: 'text',
            applied: false
          });
        }
      });
    });

    if (newRecommendations.length > 0) {
      setRecommendations(prev => {
        // Filter out duplicates
        const existingTexts = new Set(prev.map(r => r.text));
        const uniqueNew = newRecommendations.filter(r => !existingTexts.has(r.text));
        return [...prev, ...uniqueNew];
      });
    }
  };

  // Extract recommendations from formulation conflicts
  const extractRecommendationsFromConflicts = (conflicts) => {
    if (!Array.isArray(conflicts) || conflicts.length === 0) {
      return;
    }

    const newRecommendations = conflicts.map(conflict => ({
      text: conflict.detail || conflict.message || 'Constraint conflict detected',
      source: 'conflict',
      conflictType: conflict.type || 'error',
      tag: conflict.tag,
      applied: false
    }));

    setRecommendations(prev => [...prev, ...newRecommendations]);
  };

  // Format action name for display
  const formatActionName = (functionName) => {
    // Try to get localized function name from translations
    const localeKey = `rations.functionNames.${functionName}`;
    const translation = t(localeKey);

    // If translation exists and is not the same as the key, use it
    if (translation !== localeKey) {
      return translation;
    }

    // Fallback: Convert snake_case to Title Case
    return functionName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Format function result message with localization
  const formatFunctionMessage = (functionName, result) => {
    // If no result or no message, return default completion message
    if (!result || !result.message) {
      return t('rations.actionCompleted');
    }

    // Special handling for add_feed_to_ration message format
    if (functionName === 'add_feed_to_ration' && result.message && result.message.startsWith('Added ')) {
      // Extract count and feed names from the message
      // Format: "Added X feed(s) to the ration: feed1, feed2, ..."
      const match = result.message.match(/Added (\d+) feed\(s\) to the ration: (.*)/);
      if (match && match.length >= 3) {
        const count = match[1];
        const feedNames = match[2];
        return t('rations.toolMessages.addedFeedsToRation', { count, feedNames });
      }
    }

    // Special handling for remove_feed_from_ration message format
    if (functionName === 'remove_feed_from_ration' && result.message && result.message.startsWith('Removed ')) {
      // Extract count and feed names from the message
      // Format: "Removed X feed(s) from the ration: feed1, feed2, ..."
      const match = result.message.match(/Removed (\d+) feed\(s\) from the ration: (.*)/);
      if (match && match.length >= 3) {
        const count = match[1];
        const feedNames = match[2];
        return t('rations.toolMessages.removedFeedsFromRation', { count, feedNames });
      }
    }

    // Special handling for update_nutrient_constraint message format
    if (functionName === 'update_nutrient_constraint' && result.message) {
      // Check for "Updated X and added Y nutrient constraints" format
      const updatedAndAddedMatch = result.message.match(/Updated (\d+) and added (\d+) nutrient constraints: (.*)/);
      if (updatedAndAddedMatch && updatedAndAddedMatch.length >= 4) {
        const updateCount = updatedAndAddedMatch[1];
        const addCount = updatedAndAddedMatch[2];
        const constraints = updatedAndAddedMatch[3];
        return t('rations.toolMessages.updatedAndAddedNutrientConstraints', {
          updateCount,
          addCount,
          constraints
        });
      }

      // Check for "Updated X nutrient constraints" format
      const updatedMatch = result.message.match(/Updated (\d+) nutrient constraints: (.*)/);
      if (updatedMatch && updatedMatch.length >= 3) {
        const count = updatedMatch[1];
        const constraints = updatedMatch[2];
        return t('rations.toolMessages.updatedNutrientConstraints', { count, constraints });
      }

      // Check for "Added X nutrient constraints" format
      const addedMatch = result.message.match(/Added (\d+) nutrient constraints: (.*)/);
      if (addedMatch && addedMatch.length >= 3) {
        const count = addedMatch[1];
        const constraints = addedMatch[2];
        return t('rations.toolMessages.addedNutrientConstraints', { count, constraints });
      }
    }

    // Special handling for run_formulation message format
    if (functionName === 'run_formulation' && result.message) {
      // Check for "Formulation successful. Total cost: X per day." format
      const successMatch = result.message.match(/Formulation successful\. Total cost: ([\d.]+) per day\./);
      if (successMatch && successMatch.length >= 2) {
        const cost = successMatch[1];
        return t('rations.toolMessages.formulationSuccessful', { cost });
      }

      // Check for "Formulation failed" format
      const failedMatch = result.message.match(/Formulation failed: (.*)/);
      if (failedMatch && failedMatch.length >= 2) {
        const reason = failedMatch[1];
        return t('rations.toolMessages.formulationFailed', { reason });
      }
    }

    // Fallback: Return the original message
    return result.message;
  };

  // Handle stop button click
  const handleStopFormulation = () => {
    if (isAutoRunning) {
      console.log('[AIAssistantOverlay] Sending stop command to backend');

      // Immediately disable auto-running to prevent any new requests
      setIsAutoRunning(false);

      // Add a system message to indicate stop request
      const stopSystemMessage = {
        role: 'system',
        content: t('rations.statusUpdates.stoppingFormulation', 'Stopping formulation...'),
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, stopSystemMessage]);

      // Set loading state to provide visual feedback that the stop command is being processed
      setIsLoading(true);

      // Also set a flag in the current context to prevent auto-continue
      if (currentContext && onContextChange) {
        const updatedContext = {
          ...currentContext,
          is_formulation_complete: true,
          completion_reason: "Stopped by user",
          auto_continue: false
        };
        console.log('[AIAssistantOverlay] Updating context with stop flags:', updatedContext);
        onContextChange(updatedContext);

        // Send the stop command to the backend with the updated context
        sendMessageToBackend("stop", updatedContext);
      } else {
        // If no context available, still try to send stop command
        sendMessageToBackend("stop");
      }
    }
  };

  // Render conversation tab
  const renderConversation = () => {
    return (
      <div className="flex-grow overflow-y-auto p-4 space-y-4">
        {messages.map((message, index) => (
          <MessageBubble key={index} message={message} />
        ))}
        <div ref={messagesEndRef} />

        {isLoading && (
          <div className="flex justify-center items-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
            <span className="ml-2 text-gray-500">
              {isAutoRunning
                ? t('rations.statusUpdates.autoFormulating')
                : t('rations.statusUpdates.thinking')}
              {currentStatus && ` (${formatActionName(currentStatus)})`}
            </span>
          </div>
        )}

        {isAutoRunning && !isLoading && (
          <div className="flex justify-center items-center py-4">
            <div className="flex items-center bg-green-50 px-4 py-2 rounded-full border border-green-200">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-2"></div>
              <span className="text-sm text-green-700">
                {t('rations.statusUpdates.autoFormulationInProgress')}
              </span>
              <button
                onClick={handleStopFormulation}
                className="ml-3 px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                {t('common.stop', "Stop")}
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render changes tab (comparing before/after state)
  const renderChanges = () => {
    if (!currentContext || !beforeState) {
      return (
        <div className="flex-grow overflow-y-auto p-4">
          <p className="text-center text-gray-500">{t('rations.noChangesToDisplay')}</p>
        </div>
      );
    }

    // Compare before/after states
    const beforeFeeds = beforeState.feed_data || [];
    const afterFeeds = currentContext.feed_data || [];
    const beforeConstraints = beforeState.constraints || [];
    const afterConstraints = currentContext.constraints || [];

    // Find added/removed/modified feeds
    const addedFeeds = afterFeeds.filter(
      feed => !beforeFeeds.some(bf => (bf.id === feed.id) || (bf.feed_id === feed.id) ||
                                      (bf.id === feed.feed_id) || (bf.feed_id === feed.feed_id))
    );

    const removedFeeds = beforeFeeds.filter(
      feed => !afterFeeds.some(af => (af.id === feed.id) || (af.feed_id === feed.id) ||
                                     (af.id === feed.feed_id) || (af.feed_id === feed.feed_id))
    );

    const modifiedFeeds = afterFeeds.filter(afterFeed => {
      const beforeFeed = beforeFeeds.find(
        bf => (bf.id === afterFeed.id) || (bf.feed_id === afterFeed.id) ||
              (bf.id === afterFeed.feed_id) || (bf.feed_id === afterFeed.feed_id)
      );
      if (!beforeFeed) return false;

      return (
        afterFeed.min_inclusion_percentage !== beforeFeed.min_inclusion_percentage ||
        afterFeed.max_inclusion_percentage !== beforeFeed.max_inclusion_percentage ||
        afterFeed.actual_inclusion_percentage !== beforeFeed.actual_inclusion_percentage
      );
    });

    // Find added/removed/modified constraints
    const addedConstraints = afterConstraints.filter(
      constraint => !beforeConstraints.some(bc => bc.nutrient_name === constraint.nutrient_name)
    );

    const removedConstraints = beforeConstraints.filter(
      constraint => !afterConstraints.some(ac => ac.nutrient_name === constraint.nutrient_name)
    );

    const modifiedConstraints = afterConstraints.filter(afterConstraint => {
      const beforeConstraint = beforeConstraints.find(
        bc => bc.nutrient_name === afterConstraint.nutrient_name
      );
      if (!beforeConstraint) return false;

      return (
        afterConstraint.min_value !== beforeConstraint.min_value ||
        afterConstraint.max_value !== beforeConstraint.max_value
      );
    });

    // Check if formulation status changed
    const formulationStatusChanged =
      (!beforeState.formulation_result && currentContext.formulation_result) ||
      (beforeState.formulation_result?.status !== currentContext.formulation_result?.status);

    return (
      <div className="flex-grow overflow-y-auto p-4 space-y-6">
        <h3 className="text-lg font-medium text-gray-900">{t('rations.changesDetected')}</h3>

        {/* Feeds Changes */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900">
            {t('rations.feedChanges')}
          </h4>

          {addedFeeds.length === 0 && removedFeeds.length === 0 && modifiedFeeds.length === 0 ? (
            <p className="text-gray-500">{t('rations.noFeedChanges')}</p>
          ) : (
            <div className="space-y-2">
              {addedFeeds.length > 0 && (
                <div className="bg-green-50 p-3 rounded-md border border-green-100">
                  <h5 className="font-medium text-green-800">{t('rations.feedsAdded')}</h5>
                  <ul className="mt-1 space-y-1">
                    {addedFeeds.map(feed => (
                      <li key={feed.id || feed.feed_id} className="text-sm">
                        {feed.name} ({feed.min_inclusion_percentage || 0}% - {feed.max_inclusion_percentage || 100}%)
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {removedFeeds.length > 0 && (
                <div className="bg-red-50 p-3 rounded-md border border-red-100">
                  <h5 className="font-medium text-red-800">{t('rations.feedsRemoved')}</h5>
                  <ul className="mt-1 space-y-1">
                    {removedFeeds.map(feed => (
                      <li key={feed.id || feed.feed_id} className="text-sm">
                        {feed.name}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {modifiedFeeds.length > 0 && (
                <div className="bg-blue-50 p-3 rounded-md border border-blue-100">
                  <h5 className="font-medium text-blue-800">{t('rations.feedsModified')}</h5>
                  <ul className="mt-1 space-y-1">
                    {modifiedFeeds.map(feed => {
                      const beforeFeed = beforeFeeds.find(
                        bf => (bf.id === feed.id) || (bf.feed_id === feed.id) ||
                              (bf.id === feed.feed_id) || (bf.feed_id === feed.feed_id)
                      );

                      return (
                        <li key={feed.id || feed.feed_id} className="text-sm">
                          <strong>{feed.name}</strong>: {beforeFeed?.min_inclusion_percentage || 0}% - {beforeFeed?.max_inclusion_percentage || 100}%
                          {' → '}
                          {feed.min_inclusion_percentage || 0}% - {feed.max_inclusion_percentage || 100}%
                        </li>
                      );
                    })}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Constraints Changes */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900">
            {t('rations.constraintChanges')}
          </h4>

          {addedConstraints.length === 0 && removedConstraints.length === 0 && modifiedConstraints.length === 0 ? (
            <p className="text-gray-500">{t('rations.noConstraintChanges')}</p>
          ) : (
            <div className="space-y-2">
              {addedConstraints.length > 0 && (
                <div className="bg-green-50 p-3 rounded-md border border-green-100">
                  <h5 className="font-medium text-green-800">{t('rations.constraintsAdded')}</h5>
                  <ul className="mt-1 space-y-1">
                    {addedConstraints.map(constraint => (
                      <li key={constraint.nutrient_name} className="text-sm">
                        {constraint.nutrient_name}: {constraint.min_value !== null ? `min=${constraint.min_value}` : ''}
                        {constraint.min_value !== null && constraint.max_value !== null ? ', ' : ''}
                        {constraint.max_value !== null ? `max=${constraint.max_value}` : ''}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {removedConstraints.length > 0 && (
                <div className="bg-red-50 p-3 rounded-md border border-red-100">
                  <h5 className="font-medium text-red-800">{t('rations.constraintsRemoved')}</h5>
                  <ul className="mt-1 space-y-1">
                    {removedConstraints.map(constraint => (
                      <li key={constraint.nutrient_name} className="text-sm">
                        {constraint.nutrient_name}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {modifiedConstraints.length > 0 && (
                <div className="bg-blue-50 p-3 rounded-md border border-blue-100">
                  <h5 className="font-medium text-blue-800">{t('rations.constraintsModified')}</h5>
                  <ul className="mt-1 space-y-1">
                    {modifiedConstraints.map(constraint => {
                      const beforeConstraint = beforeConstraints.find(
                        bc => bc.nutrient_name === constraint.nutrient_name
                      );

                      return (
                        <li key={constraint.nutrient_name} className="text-sm">
                          <strong>{constraint.nutrient_name}</strong>:
                          {' '}
                          {beforeConstraint?.min_value !== null ? `min=${beforeConstraint.min_value}` : t('rations.noMin')}
                          {beforeConstraint?.min_value !== null && beforeConstraint?.max_value !== null ? ', ' : ''}
                          {beforeConstraint?.max_value !== null ? `max=${beforeConstraint.max_value}` : t('rations.noMax')}
                          {' → '}
                          {constraint.min_value !== null ? `min=${constraint.min_value}` : t('rations.noMin')}
                          {constraint.min_value !== null && constraint.max_value !== null ? ', ' : ''}
                          {constraint.max_value !== null ? `max=${constraint.max_value}` : t('rations.noMax')}
                        </li>
                      );
                    })}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Formulation Result Changes */}
        {formulationStatusChanged && (
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900">
              {t('rations.formulationChanges')}
            </h4>

            <div className="bg-yellow-50 p-3 rounded-md border border-yellow-100">
              <h5 className="font-medium text-yellow-800">{t('rations.formulationStatus')}</h5>
              <p className="mt-1 text-sm">
                {beforeState.formulation_result
                  ? `${beforeState.formulation_result.status || t('rations.none')} → ${currentContext.formulation_result?.status || t('rations.none')}`
                  : t('rations.rationFormulated')}
              </p>

              {currentContext.formulation_result?.actual_total_cost && (
                <p className="mt-2 text-sm">
                  <strong>{t('rations.totalCost')}:</strong> {formatPrice(currentContext.formulation_result.actual_total_cost, i18n.language)}
                </p>
              )}
            </div>
          </div>
        )}

        {/* AI Actions Summary */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900">
            {t('rations.aiActionsSummary')}
          </h4>

          {aiActions.length === 0 ? (
            <p className="text-gray-500">{t('rations.noAiActions')}</p>
          ) : (
            <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
              <ul className="space-y-2">
                {aiActions.map((action, index) => (
                  <li key={index} className="text-sm">
                    <div className="flex items-center">
                      <span className="font-medium">{action.description}</span>
                      <span className="ml-2 text-xs text-gray-500">
                        {new Date(action.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    {action.result && (
                      <p className="text-xs text-gray-600 mt-1">{formatFunctionMessage(action.name, action.result)}</p>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render analysis tab (recommendations)
  const renderAnalysis = () => {
    return (
      <div className="flex-grow overflow-y-auto p-4 space-y-6">
        <h3 className="text-lg font-medium text-gray-900">{t('rations.aiAnalysis')}</h3>

        {/* Recommendations */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900">
            {t('rations.recommendations')}
          </h4>

          {recommendations.length === 0 ? (
            <p className="text-gray-500">{t('rations.noRecommendations')}</p>
          ) : (
            <div className="space-y-2">
              {recommendations.map((recommendation, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-md border ${
                    recommendation.source === 'conflict'
                      ? 'bg-red-50 border-red-100'
                      : 'bg-blue-50 border-blue-100'
                  }`}
                >
                  <p className="text-sm">
                    {recommendation.text}
                  </p>
                  {recommendation.tag && (
                    <p className="text-xs mt-1 font-medium">
                      {recommendation.tag}
                    </p>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Formulation Result Summary */}
        {currentContext?.formulation_result && (
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900">
              {t('rations.formulationSummary')}
            </h4>

            <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium">{t('rations.status')}</p>
                  <p className="text-sm">
                    {currentContext.formulation_result.status === 'success'
                      ? t('common.success')
                      : currentContext.formulation_result.status}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium">{t('rations.totalCost')}</p>
                  <p className="text-sm">
                    {formatPrice(currentContext.formulation_result.actual_total_cost, i18n.language)}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium">{t('rations.feedsUsed')}</p>
                  <p className="text-sm">
                    {Object.keys(currentContext.formulation_result.feeds || {}).length}
                  </p>
                </div>
              </div>

              {currentContext.formulation_result.message && (
                <p className="text-sm mt-3">
                  {currentContext.formulation_result.message}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render individual message bubbles
  const MessageBubble = ({ message }) => {
    // Format system messages (like function calls)
    const formatSystemMessage = (msg) => {
      if (msg.functionInfo) {
        const functionName = msg.functionInfo.name;
        const result = msg.functionInfo.result;

        // Determine styling based on function or result
        let bgColor = 'bg-gray-100';
        let textColor = 'text-gray-700';
        let title = formatActionName(functionName);

        switch (functionName) {
          case 'add_feed_to_ration':
            bgColor = 'bg-blue-50 border-blue-100';
            textColor = 'text-blue-700';
            break;
          case 'remove_feed_from_ration':
            bgColor = 'bg-orange-50 border-orange-100';
            textColor = 'text-orange-700';
            break;
          case 'update_nutrient_constraint':
            bgColor = 'bg-purple-50 border-purple-100';
            textColor = 'text-purple-700';
            break;
          case 'run_formulation':
            bgColor = result?.status === 'success'
              ? 'bg-green-50 border-green-100'
              : 'bg-yellow-50 border-yellow-100';
            textColor = result?.status === 'success'
              ? 'text-green-700'
              : 'text-yellow-700';
            break;
          case 'save_formulation':
            bgColor = 'bg-green-50 border-green-100';
            textColor = 'text-green-700';
            break;
          case 'analyze_formulation_result':
            bgColor = 'bg-indigo-50 border-indigo-100';
            textColor = 'text-indigo-700';
            break;
          case 'check_nutrition':
            bgColor = 'bg-teal-50 border-teal-100';
            textColor = 'text-teal-700';
            break;
          case 'add_nutrients_to_feed':
            bgColor = 'bg-blue-50 border-blue-100';
            textColor = 'text-blue-700';
            break;
          case 'make_ration':
            bgColor = 'bg-green-50 border-green-100';
            textColor = 'text-green-700';
            break;
          default:
            bgColor = 'bg-gray-100 border-gray-200';
            textColor = 'text-gray-600';
        }

        return (
          <div className={`${bgColor} p-2 rounded border text-xs`}>
            <span className={`font-medium ${textColor}`}>{title}</span>
            <p className="mt-1">{formatFunctionMessage(functionName, result)}</p>

            {/* Display conflicts if present */}
            {result?.conflicts && result.conflicts.length > 0 && (
              <div className="mt-1 text-red-700">
                {t('rations.conflictsDetected', { count: result.conflicts.length })}
              </div>
            )}
          </div>
        );
      }

      if (msg.error) {
        return (
          <div className="bg-red-50 p-2 rounded border border-red-100 text-xs">
            <span className="font-medium text-red-700">{t('common.error')}</span>
            <p className="mt-1">{msg.content}</p>
          </div>
        );
      }

      return (
        <div className="text-gray-500 text-xs markdown-content">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              code({ node, inline, className, children, ...props }) {
                const match = /language-(\w+)/.exec(className || '')
                return !inline && match ? (
                  <SyntaxHighlighter
                    style={nord}
                    language={match[1]}
                    PreTag="div"
                    {...props}
                  >
                    {String(children).replace(/\n$/, '')}
                  </SyntaxHighlighter>
                ) : (
                  <code className={className} {...props}>
                    {children}
                  </code>
                )
              }
            }}
          >
            {msg.content}
          </ReactMarkdown>
        </div>
      );
    };

    return (
      <div className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
        <div className={`max-w-[80%] p-3 rounded-lg shadow-sm ${
          message.role === 'user'
            ? 'bg-green-100 text-gray-800'
            : message.role === 'assistant'
              ? message.isThinking
                ? 'bg-gray-50 border border-gray-200 text-gray-600 italic'
                : 'bg-white border border-gray-200 text-gray-800'
              : 'bg-transparent border-none p-0'
        }`}>
          {message.role === 'system'
            ? formatSystemMessage(message)
            : <div className="text-sm markdown-content">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    // Code block syntax highlighting
                    code({ node, inline, className, children, ...props }) {
                      const match = /language-(\w+)/.exec(className || '')
                      return !inline && match ? (
                        <SyntaxHighlighter
                          style={nord}
                          language={match[1]}
                          PreTag="div"
                          {...props}
                        >
                          {String(children).replace(/\n$/, '')}
                        </SyntaxHighlighter>
                      ) : (
                        <code className={className} {...props}>
                          {children}
                        </code>
                      )
                    },
                    // Consistent table styling
                    table: ({ node, ...props }) => (
                      <div className="overflow-x-auto my-4 rounded-lg border border-gray-200 shadow-sm">
                        <table className="min-w-full divide-y divide-gray-200 table-auto" {...props} />
                      </div>
                    ),
                    thead: ({ node, ...props }) => (
                      <thead className="bg-gray-50" {...props} />
                    ),
                    th: ({ node, ...props }) => (
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b" {...props} />
                    ),
                    td: ({ node, ...props }) => (
                      <td className="px-3 py-2 text-sm text-gray-700 align-top break-words" {...props} />
                    ),
                    tr: ({ node, ...props }) => (
                      <tr className="hover:bg-gray-100 transition-colors even:bg-gray-50" {...props} />
                    )
                  }}
                >
                  {message.content}
                </ReactMarkdown>
              </div>
          }
        </div>
      </div>
    );
  };

  // Render the main component
  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center transition-opacity duration-300 ${isActive ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
      <div className="bg-gray-50 rounded-lg shadow-xl w-11/12 md:w-4/5 max-w-5xl h-[90vh] flex flex-col">
        {/* Header */}
        <div className="border-b border-gray-200 bg-white sticky top-0 z-10">
          <div className="flex justify-between items-center px-4 py-3">
            <h2 className="text-xl font-semibold">{t('rations.aiAssistant')}</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="text-gray-600 hover:text-gray-800"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span className="sr-only">{t('common.close')}</span>
            </Button>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-gray-200 px-4">
            <button
              className={`px-3 py-2 text-sm font-medium ${
                activeTab === 'conversation'
                  ? 'border-b-2 border-green-500 text-green-600'
                  : 'text-gray-500 hover:text-gray-700 border-b-2 border-transparent'
              }`}
              onClick={() => setActiveTab('conversation')}
            >
              {t('rations.aiTabs.conversation')}
            </button>

            <button
              className={`px-3 py-2 text-sm font-medium flex items-center ${
                activeTab === 'changes'
                  ? 'border-b-2 border-green-500 text-green-600'
                  : 'text-gray-500 hover:text-gray-700 border-b-2 border-transparent'
              }`}
              onClick={() => setActiveTab('changes')}
            >
              {t('rations.aiTabs.changes')}
              {madeChanges && aiActions.length > 0 && (
                <span className="ml-1.5 inline-flex items-center justify-center h-5 w-5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {aiActions.length}
                </span>
              )}
            </button>

            <button
              className={`px-3 py-2 text-sm font-medium flex items-center ${
                activeTab === 'analysis'
                  ? 'border-b-2 border-green-500 text-green-600'
                  : 'text-gray-500 hover:text-gray-700 border-b-2 border-transparent'
              }`}
              onClick={() => setActiveTab('analysis')}
            >
              {t('rations.aiTabs.analysis')}
              {recommendations.length > 0 && (
                <span className="ml-1.5 inline-flex items-center justify-center h-5 w-5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  {recommendations.length}
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Tab content */}
        <div className="flex-grow flex flex-col overflow-hidden">
          {activeTab === 'conversation' && renderConversation()}
          {activeTab === 'changes' && renderChanges()}
          {activeTab === 'analysis' && renderAnalysis()}
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-white sticky bottom-0 z-10">
          {activeTab === 'conversation' ? (
            <>
              <div className="flex space-x-2">
                {isAutoRunning ? (
                  <div className="flex-grow flex items-center justify-between bg-gray-50 px-4 py-2 rounded-md border border-gray-200">
                    <span className="text-sm text-gray-600">
                      {t('rations.statusUpdates.autoFormulationActive')}
                    </span>
                    <Button
                      variant="danger"
                      size="sm"
                      onClick={handleStopFormulation}
                    >
                      {t('common.stop', "Stop")}
                    </Button>
                  </div>
                ) : (
                  <>
                    <textarea
                      className="flex-grow rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 text-sm"
                      rows={2}
                      placeholder={t('rations.askAIPlaceholder', "Ask me anything about your ration...")}
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      disabled={isLoading || !currentContext || isAutoRunning}
                    />
                    <Button
                      onClick={sendMessage}
                      disabled={isLoading || !input.trim() || !currentContext || isAutoRunning}
                      className="self-end h-10"
                    >
                      {isLoading && messages.length > 0 && messages[messages.length-1].role === 'user'
                        ? t('common.thinking', "Thinking...")
                        : t('common.send', "Send")}
                    </Button>
                  </>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {isAutoRunning
                  ? t('rations.statusUpdates.autoFormulationHint')
                  : t('rations.aiAssistantHint')}
              </p>
            </>
          ) : (
            <div className="flex justify-end">
              {madeChanges ? (
                <Button
                  variant="primary"
                  onClick={() => {
                    console.log("[AI Overlay] Apply Changes clicked. Passing context to parent:", currentContext);
                    onClose(); // Call the onClose to close the overlay
                  }}
                  disabled={isLoading}
                >
                  {t('rations.applyChanges', "Apply Changes")}
                </Button>
              ) : (
                <p className="text-sm text-gray-500 italic">
                  {t('rations.noChangesToApply', "No changes to apply")}
                </p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIAssistantOverlay;